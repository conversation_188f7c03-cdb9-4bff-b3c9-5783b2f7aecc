<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 快捷指令功能修复验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #4285f4;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #333;
            margin-top: 0;
            border-bottom: 2px solid #4285f4;
            padding-bottom: 10px;
        }

        .step {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #28a745;
        }

        .step.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }

        .step.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .code {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #3367d6;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-warning {
            background: #ffc107;
            color: #333;
        }

        .btn-danger {
            background: #dc3545;
        }

        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .checklist {
            list-style: none;
            padding: 0;
        }

        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .checklist li:before {
            content: "☐ ";
            color: #666;
            font-weight: bold;
            margin-right: 8px;
        }

        .checklist li.checked:before {
            content: "✅ ";
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 快捷指令功能修复验证</h1>

        <div class="test-section">
            <h2>📋 修复内容概述</h2>
            <div class="step">
                <strong>已修复的问题：</strong>
                <ul>
                    <li>✅ 新增快捷指令功能：增加了详细的调试日志和错误处理</li>
                    <li>✅ 编辑快捷指令功能：增加了URL验证和更新时间戳</li>
                    <li>✅ 导入配置功能：增加了文件格式验证和详细的错误提示</li>
                    <li>✅ 事件绑定：增加了事件防冒泡和详细的绑定日志</li>
                    <li>✅ 调试接口：增加了更多调试和测试功能</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>🚀 快速验证步骤</h2>
            
            <div class="step">
                <strong>步骤1：重新加载插件</strong>
                <div class="code">
                    1. 打开 chrome://extensions/<br>
                    2. 找到"实时指令插件"<br>
                    3. 点击刷新按钮 🔄
                </div>
            </div>

            <div class="step">
                <strong>步骤2：打开调试控制台</strong>
                <div class="code">
                    1. 点击插件图标<br>
                    2. 右键点击popup → 检查<br>
                    3. 切换到Console标签页
                </div>
            </div>

            <div class="step">
                <strong>步骤3：验证事件绑定</strong>
                <div class="code">
                    查看控制台应该显示：<br>
                    ✅ 快捷指令管理按钮事件已绑定<br>
                    ✅ 添加快捷指令按钮事件已绑定<br>
                    ✅ 导入按钮事件已绑定<br>
                    ✅ 快捷指令管理界面事件设置完成
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 功能测试清单</h2>
            <ul class="checklist">
                <li id="test-1">打开快捷指令管理界面</li>
                <li id="test-2">添加新快捷指令（输入"测试指令" + "https://test.com"）</li>
                <li id="test-3">查看新指令是否出现在列表中</li>
                <li id="test-4">编辑现有指令的URL</li>
                <li id="test-5">删除一个指令</li>
                <li id="test-6">导出配置文件</li>
                <li id="test-7">导入测试配置文件</li>
                <li id="test-8">验证导入的指令是否正确显示</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔍 调试命令测试</h2>
            <div class="step">
                <strong>在控制台中运行以下命令进行测试：</strong>
                <button class="btn" onclick="runDebugTest('getShortcuts')">测试获取快捷指令</button>
                <button class="btn" onclick="runDebugTest('addTest')">测试添加功能</button>
                <button class="btn" onclick="runDebugTest('importTest')">测试导入功能</button>
                <button class="btn btn-warning" onclick="runDebugTest('showManager')">显示管理界面</button>
            </div>
            
            <div id="debug-output" class="code" style="display: none;">
                调试输出将显示在这里...
            </div>
        </div>

        <div class="test-section">
            <h2>📊 测试结果</h2>
            <div id="test-results">
                <div class="status info">
                    等待测试开始...点击上方按钮开始测试各项功能
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🆘 常见问题解决</h2>
            <div class="step warning">
                <strong>如果按钮点击没有反应：</strong>
                <ol>
                    <li>检查控制台是否有错误信息</li>
                    <li>确认事件绑定日志是否正常</li>
                    <li>尝试手动运行调试命令</li>
                </ol>
            </div>
            
            <div class="step warning">
                <strong>如果添加指令失败：</strong>
                <ol>
                    <li>检查输入框是否有内容</li>
                    <li>确认URL格式是否正确</li>
                    <li>查看详细的错误日志</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // 测试结果记录
        let testResults = [];

        // 运行调试测试
        function runDebugTest(testType) {
            const output = document.getElementById('debug-output');
            const results = document.getElementById('test-results');
            
            output.style.display = 'block';
            
            try {
                switch(testType) {
                    case 'getShortcuts':
                        if (typeof window.popupDebug !== 'undefined') {
                            const shortcuts = window.popupDebug.getShortcuts();
                            output.innerHTML = `快捷指令数量: ${shortcuts.size}<br>` + 
                                             `指令列表: ${Array.from(shortcuts.keys()).join(', ')}`;
                            addTestResult('获取快捷指令', true, `成功获取${shortcuts.size}条指令`);
                        } else {
                            throw new Error('popupDebug接口不可用');
                        }
                        break;
                        
                    case 'addTest':
                        if (typeof window.popupDebug !== 'undefined') {
                            // 先填入测试数据
                            const cmdInput = document.getElementById('newShortcutCommand');
                            const urlInput = document.getElementById('newShortcutUrl');
                            
                            if (cmdInput && urlInput) {
                                cmdInput.value = '验证测试指令';
                                urlInput.value = 'https://verification-test.com';
                                window.popupDebug.testAddShortcut();
                                output.innerHTML = '已触发添加测试，请查看控制台日志';
                                addTestResult('添加功能测试', true, '测试指令已触发');
                            } else {
                                throw new Error('找不到输入框元素');
                            }
                        } else {
                            throw new Error('popupDebug接口不可用');
                        }
                        break;
                        
                    case 'importTest':
                        if (typeof window.popupDebug !== 'undefined') {
                            const testData = {
                                shortcuts: {
                                    "验证导入指令": {
                                        "url": "https://import-test.com",
                                        "description": "导入测试",
                                        "isDefault": false,
                                        "createdAt": Date.now()
                                    }
                                },
                                usageStats: {},
                                version: "1.0.0"
                            };
                            window.popupDebug.testImport(testData);
                            output.innerHTML = '已触发导入测试，请查看控制台日志';
                            addTestResult('导入功能测试', true, '测试数据已触发导入');
                        } else {
                            throw new Error('popupDebug接口不可用');
                        }
                        break;
                        
                    case 'showManager':
                        if (typeof window.popupDebug !== 'undefined') {
                            window.popupDebug.showShortcutManager();
                            output.innerHTML = '已触发显示管理界面';
                            addTestResult('显示管理界面', true, '管理界面应该已显示');
                        } else {
                            throw new Error('popupDebug接口不可用');
                        }
                        break;
                }
            } catch (error) {
                output.innerHTML = `错误: ${error.message}`;
                addTestResult(testType, false, error.message);
            }
        }

        // 添加测试结果
        function addTestResult(testName, success, message) {
            const results = document.getElementById('test-results');
            const statusClass = success ? 'success' : 'error';
            const icon = success ? '✅' : '❌';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${statusClass}`;
            resultDiv.innerHTML = `${icon} ${testName}: ${message}`;
            
            results.appendChild(resultDiv);
            
            // 记录结果
            testResults.push({
                test: testName,
                success: success,
                message: message,
                timestamp: new Date().toLocaleTimeString()
            });
        }

        // 标记清单项为完成
        function markTestComplete(testId) {
            const item = document.getElementById(testId);
            if (item) {
                item.classList.add('checked');
            }
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', () => {
            addTestResult('页面加载', true, '验证页面已准备就绪，请开始测试');
        });
    </script>
</body>
</html>
