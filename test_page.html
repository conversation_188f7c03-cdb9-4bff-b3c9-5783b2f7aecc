<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome插件点击功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .section h2 {
            color: #333;
            margin-top: 0;
        }

        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .login-btn {
            background: #007bff;
            color: white;
        }

        .submit-btn {
            background: #28a745;
            color: white;
        }

        .cancel-btn {
            background: #dc3545;
            color: white;
        }

        .search-btn {
            background: #ffc107;
            color: black;
        }

        .red-btn {
            background: #ff4444;
            color: white;
        }

        .blue-btn {
            background: #4444ff;
            color: white;
        }

        .green-btn {
            background: #44ff44;
            color: black;
        }

        .big-btn {
            padding: 15px 30px;
            font-size: 18px;
        }

        .small-btn {
            padding: 5px 10px;
            font-size: 12px;
        }

        input, textarea {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }

        input[type="text"], input[type="password"], input[type="email"] {
            width: 200px;
        }

        textarea {
            width: 300px;
            height: 80px;
        }

        .form-group {
            margin: 15px 0;
        }

        label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }

        a {
            color: #007bff;
            text-decoration: none;
            margin: 0 10px;
        }

        a:hover {
            text-decoration: underline;
        }

        .hidden {
            display: none;
        }

        .result {
            margin-top: 20px;
            padding: 10px;
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }

        .menu {
            background: #333;
            color: white;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
            display: inline-block;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🧪 Chrome插件点击功能测试页面</h1>
    <p>这个页面包含各种类型的可点击元素，用于测试Chrome插件的智能点击功能。</p>

    <!-- 智能解析测试区域 -->
    <div class="section">
        <h2>🧠 智能解析测试区域</h2>
        <p><strong>自然语言指令测试：</strong></p>
        <ul>
            <li><code>请帮我点击那个红色的提交按钮</code></li>
            <li><code>把我的名字李明输入到姓名框里去</code></li>
            <li><code>帮忙点击一下大的搜索按钮</code></li>
            <li><code>在用户名那里输入张三</code></li>
            <li><code>选择蓝色的取消按钮</code></li>
            <li><code>点那个小的登录按钮</code></li>
        </ul>

        <!-- 带修饰词的按钮 -->
        <button type="button" class="red-btn" onclick="showResult('红色提交按钮被点击了！')">提交</button>
        <button type="button" class="blue-btn" onclick="showResult('蓝色取消按钮被点击了！')">取消</button>
        <button type="button" class="green-btn" onclick="showResult('绿色确定按钮被点击了！')">确定</button>

        <br><br>

        <!-- 不同大小的按钮 -->
        <button type="button" class="big-btn search-btn" onclick="showResult('大搜索按钮被点击了！')">搜索</button>
        <button type="button" class="small-btn login-btn" onclick="showResult('小登录按钮被点击了！')">登录</button>

        <br><br>

        <!-- 带位置描述的输入框 -->
        <div style="display: flex; gap: 20px; align-items: center;">
            <div>
                <label>左边的姓名框：</label>
                <input type="text" placeholder="请输入姓名" onfocus="showResult('姓名框获得焦点！')" oninput="showInputResult(this, '姓名')">
            </div>
            <div>
                <label>右边的用户名框：</label>
                <input type="text" placeholder="请输入用户名" onfocus="showResult('用户名框获得焦点！')" oninput="showInputResult(this, '用户名')">
            </div>
        </div>
    </div>

    <!-- 按钮测试区域 -->
    <div class="section">
        <h2>🔘 按钮测试区域</h2>
        <p>测试指令：<code>点击登录按钮</code>、<code>点击提交按钮</code>、<code>点击取消按钮</code>、<code>点击搜索按钮</code></p>

        <button type="button" class="login-btn" onclick="showResult('登录按钮被点击了！')">登录</button>
        <button type="button" class="submit-btn" onclick="showResult('提交按钮被点击了！')">提交</button>
        <button type="button" class="cancel-btn" onclick="showResult('取消按钮被点击了！')">取消</button>
        <button type="button" class="search-btn" onclick="showResult('搜索按钮被点击了！')">搜索</button>

        <br><br>

        <!-- 不同类型的按钮 -->
        <input type="submit" value="提交表单" onclick="showResult('提交表单按钮被点击了！')">
        <input type="button" value="普通按钮" onclick="showResult('普通按钮被点击了！')">

        <br><br>

        <!-- 带role属性的按钮 -->
        <div role="button" onclick="showResult('角色按钮被点击了！')" style="display: inline-block; padding: 10px; background: #6c757d; color: white; border-radius: 4px; cursor: pointer;">角色按钮</div>
    </div>

    <!-- 输入框测试区域 -->
    <div class="section">
        <h2>📝 输入框测试区域</h2>
        <p><strong>点击测试指令：</strong><code>点击用户名输入框</code>、<code>点击密码输入框</code>、<code>点击邮箱输入框</code></p>
        <p><strong>输入测试指令：</strong></p>
        <ul>
            <li><code>在用户名输入框输入 张三</code></li>
            <li><code>输入密码 123456 到密码框</code></li>
            <li><code>在邮箱输入框输入 <EMAIL></code></li>
            <li><code>在搜索框输入 Chrome插件</code></li>
            <li><code>输入 Hello World</code> (使用当前聚焦的输入框)</li>
        </ul>

        <div class="form-group">
            <label for="username">用户名：</label>
            <input type="text" id="username" name="username" placeholder="请输入用户名"
                   onfocus="showResult('用户名输入框获得焦点！')"
                   oninput="showInputResult(this, '用户名')">
        </div>

        <div class="form-group">
            <label for="password">密码：</label>
            <input type="password" id="password" name="password" placeholder="请输入密码"
                   onfocus="showResult('密码输入框获得焦点！')"
                   oninput="showInputResult(this, '密码')">
        </div>

        <div class="form-group">
            <label for="email">邮箱：</label>
            <input type="email" id="email" name="email" placeholder="请输入邮箱地址"
                   onfocus="showResult('邮箱输入框获得焦点！')"
                   oninput="showInputResult(this, '邮箱')">
        </div>

        <div class="form-group">
            <label for="search">搜索：</label>
            <input type="search" id="search" name="search" placeholder="搜索内容"
                   onfocus="showResult('搜索输入框获得焦点！')"
                   oninput="showInputResult(this, '搜索')">
        </div>

        <div class="form-group">
            <label for="comment">评论：</label>
            <textarea id="comment" name="comment" placeholder="请输入评论内容"
                      onfocus="showResult('评论输入框获得焦点！')"
                      oninput="showInputResult(this, '评论')"></textarea>
        </div>
    </div>

    <!-- 链接测试区域 -->
    <div class="section">
        <h2>🔗 链接测试区域</h2>
        <p>测试指令：<code>点击首页链接</code>、<code>点击帮助链接</code>、<code>点击关于我们链接</code></p>

        <a href="#" onclick="showResult('首页链接被点击了！'); return false;">首页</a>
        <a href="#" onclick="showResult('帮助链接被点击了！'); return false;">帮助</a>
        <a href="#" onclick="showResult('关于我们链接被点击了！'); return false;">关于我们</a>
        <a href="#" onclick="showResult('联系我们链接被点击了！'); return false;">联系我们</a>

        <br><br>

        <!-- 带role属性的链接 -->
        <span role="link" onclick="showResult('角色链接被点击了！')" style="color: #007bff; cursor: pointer; text-decoration: underline;">角色链接</span>
    </div>

    <!-- 通用元素测试区域 -->
    <div class="section">
        <h2>🎯 通用元素测试区域</h2>
        <p>测试指令：<code>点击菜单</code>、<code>点击设置</code>、<code>点击头像</code></p>

        <div class="menu" onclick="showResult('菜单被点击了！')">菜单</div>
        <div class="menu" onclick="showResult('设置被点击了！')">设置</div>
        <div class="menu" onclick="showResult('头像被点击了！')">👤 头像</div>

        <br><br>

        <!-- 带tabindex的元素 -->
        <div tabindex="0" onclick="showResult('可聚焦元素被点击了！')" style="display: inline-block; padding: 10px; background: #17a2b8; color: white; border-radius: 4px; cursor: pointer;">可聚焦元素</div>
    </div>

    <!-- 隐藏元素测试区域 -->
    <div class="section">
        <h2>👻 隐藏元素测试区域</h2>
        <p>测试指令：<code>点击隐藏按钮</code> - 应该找不到隐藏的元素</p>

        <button class="hidden" onclick="showResult('隐藏按钮被点击了！')">隐藏按钮</button>
        <p>上面有一个隐藏的按钮，插件应该找不到它。</p>

        <button onclick="toggleHidden()">显示/隐藏按钮</button>
    </div>

    <!-- 结果显示区域 -->
    <div id="result" class="result" style="display: none;">
        <h3>📊 操作结果</h3>
        <p id="resultText"></p>
        <p><small>时间：<span id="resultTime"></span></small></p>
    </div>

    <!-- 滚动测试区域 -->
    <div class="section">
        <h2>📜 滚动测试区域</h2>
        <p><strong>滚动测试指令：</strong></p>
        <ul>
            <li><code>滚动到底部</code> - 滚动到页面最底部</li>
            <li><code>滚动到顶部</code> - 滚动到页面最顶部</li>
            <li><code>滚动到页面中间</code> - 滚动到页面中间位置</li>
            <li><code>向下滚动一点</code> - 向下滚动100px</li>
            <li><code>向上滚动一点</code> - 向上滚动100px</li>
            <li><code>向下滚动</code> - 向下滚动200px（默认）</li>
            <li><code>向上滚动</code> - 向上滚动200px（默认）</li>
            <li><code>快速向下滚动</code> - 快速向下滚动500px</li>
            <li><code>慢慢向上滚动</code> - 缓慢向上滚动（1秒动画）</li>
            <li><code>向下翻页</code> - 按屏幕高度向下滚动</li>
            <li><code>向上翻页</code> - 按屏幕高度向上滚动</li>
        </ul>

        <div style="height: 200px; background: linear-gradient(45deg, #f0f0f0, #e0e0e0); display: flex; align-items: center; justify-content: center; margin: 20px 0; border-radius: 8px;">
            <p style="font-size: 18px; color: #666;">📜 滚动测试区域 - 这里是页面中间部分</p>
        </div>

        <p>使用上述滚动指令测试页面滚动功能。观察控制台日志了解滚动执行详情。</p>
    </div>

    <!-- 长内容区域 -->
    <div class="section">
        <h2>📄 长内容区域（用于滚动测试）</h2>
        <p>这个区域包含大量内容，用于测试滚动功能。</p>

        <div style="height: 300px; background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 8px; overflow-y: auto;">
            <h3>内容区域1</h3>
            <p>这是第一个内容区域。Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        </div>

        <div style="height: 300px; background: #e3f2fd; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <h3>内容区域2</h3>
            <p>这是第二个内容区域。Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
            <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
            <p>Totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
        </div>

        <div style="height: 300px; background: #f3e5f5; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <h3>内容区域3</h3>
            <p>这是第三个内容区域。Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit.</p>
            <p>Sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
            <p>Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit.</p>
        </div>

        <div style="height: 300px; background: #e8f5e8; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <h3>内容区域4</h3>
            <p>这是第四个内容区域。Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam.</p>
            <p>Nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur.</p>
            <p>Vel illum qui dolorem eum fugiat quo voluptas nulla pariatur?</p>
        </div>

        <div style="height: 300px; background: #fff3e0; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <h3>内容区域5（页面底部附近）</h3>
            <p>这是最后一个内容区域，位于页面底部附近。</p>
            <p>使用"滚动到底部"指令可以快速到达这里。</p>
            <p>使用"滚动到顶部"指令可以快速返回页面顶部。</p>
            <p style="font-weight: bold; color: #ff5722;">🎯 恭喜！您已经到达页面底部！</p>
        </div>
    </div>

    <!-- 测试说明 -->
    <div class="section">
        <h2>📋 测试说明</h2>
        <ol>
            <li>安装并启用Chrome插件</li>
            <li>打开插件popup界面</li>
            <li>输入上述测试指令</li>
            <li>观察页面元素的高亮效果和操作结果</li>
            <li>查看浏览器控制台的详细日志</li>
        </ol>

        <h3>🎯 预期行为</h3>
        <ul>
            <li>目标元素会被彩色边框高亮显示</li>
            <li>0.5秒后执行实际操作</li>
            <li>按钮会被点击，输入框会获得焦点</li>
            <li>页面会显示操作结果</li>
            <li>控制台会输出详细的执行日志</li>
            <li><strong>滚动指令会平滑滚动到指定位置</strong></li>
        </ul>
    </div>

    <script>
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            const resultText = document.getElementById('resultText');
            const resultTime = document.getElementById('resultTime');

            resultText.textContent = message;
            resultTime.textContent = new Date().toLocaleTimeString();
            resultDiv.style.display = 'block';

            // 3秒后自动隐藏
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 3000);
        }

        function showInputResult(element, fieldName) {
            const value = element.value;
            const message = `${fieldName}输入框内容已更新: "${value}"`;
            showResult(message);

            // 在控制台也输出详细信息
            console.log('📝 输入框内容变化:', {
                字段名: fieldName,
                元素ID: element.id,
                元素类型: element.type,
                当前值: value,
                值长度: value.length,
                时间: new Date().toLocaleTimeString()
            });
        }

        function toggleHidden() {
            const hiddenButton = document.querySelector('.hidden');
            hiddenButton.classList.toggle('hidden');
        }

        // 页面加载完成提示
        window.addEventListener('load', () => {
            console.log('🧪 测试页面加载完成，可以开始测试Chrome插件的点击功能');
            showResult('测试页面加载完成，可以开始测试点击功能！');
        });
    </script>
</body>
</html>
