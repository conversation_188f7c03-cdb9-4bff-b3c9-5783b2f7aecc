/**
 * 实时指令插件 - Content Script
 * 注入到网页中，负责接收和执行来自popup的指令
 */

// 全局状态管理
let isInitialized = false;
let commandHistory = [];
let lastExecutedCommand = '';
let debugMode = false;

/**
 * 初始化content script
 */
function initializeContentScript() {
    if (isInitialized) {
        console.log('🔄 Content script已经初始化，跳过重复初始化');
        return;
    }

    console.log('🚀 Content script初始化开始...');

    // 设置消息监听器
    setupMessageListener();

    // 创建调试面板（如果需要）
    if (debugMode) {
        createDebugPanel();
    }

    // 标记为已初始化
    isInitialized = true;

    // 向页面注入标识
    injectPageIdentifier();

    console.log('✅ Content script初始化完成');
    console.log('📍 当前页面URL:', window.location.href);
    console.log('📄 页面标题:', document.title);
}

/**
 * 设置消息监听器
 * 使用chrome.runtime.onMessage.addListener监听来自popup.js的消息
 */
function setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {

        try {
            // 首先验证消息格式
            if (!message || message.type !== 'COMMAND_FROM_POPUP') {
                console.warn('⚠️ 无效的消息格式:', {
                    received: message,
                    expectedType: 'COMMAND_FROM_POPUP',
                    actualType: message?.type || 'undefined'
                });
                sendResponse({
                    success: false,
                    error: '无效的消息格式',
                    expectedType: 'COMMAND_FROM_POPUP',
                    receivedType: message?.type || 'undefined'
                });
                return true;
            }

            // 提取消息中的关键信息
            const {
                command,           // 指令内容
                isRealtime,        // 是否实时发送
                action,            // 动作类型
                inputMethod,       // 输入方式：voice/keyboard
                timestamp,         // 时间戳
                tabId,             // 标签页ID
                tabUrl,            // 标签页URL
                version            // 版本信息
            } = message;

            // 详细的调试信息输出
            const inputIcon = inputMethod === 'voice' ? '🎤' : '⌨️';
            const realtimeIcon = isRealtime ? '⚡' : '👆';

            console.log('📨 收到popup指令:', {
                command: command,
                isRealtime: isRealtime,
                inputMethod: inputMethod,
                timestamp: timestamp,
                action: action,
                tabId: tabId,
                tabUrl: tabUrl
            });

            // 根据输入方式显示不同的日志标识
            if (command) {
                console.log(`${inputIcon} ${realtimeIcon} [${inputMethod?.toUpperCase() || 'UNKNOWN'}指令] "${command}"`);
            }

            // 处理不同类型的动作
            switch (action) {
                case 'ping':
                    handlePing(message, sendResponse);
                    break;
                case 'execute':
                    handleCommandExecution(message, sendResponse);
                    break;
                case 'clear':
                    handleClear(message, sendResponse);
                    break;
                default:
                    console.warn('⚠️ 未知的动作类型:', {
                        action: action,
                        supportedActions: ['ping', 'execute', 'clear']
                    });
                    sendResponse({
                        success: false,
                        error: `未知的动作类型: ${action}`,
                        supportedActions: ['ping', 'execute', 'clear']
                    });
            }

        } catch (error) {
            console.error('❌ 处理消息时发生错误:', {
                error: error.message,
                stack: error.stack,
                message: message,
                timestamp: Date.now()
            });

            // 确保即使出错也能正常响应popup的消息
            sendResponse({
                success: false,
                error: `消息处理错误: ${error.message}`,
                stack: error.stack,
                timestamp: Date.now()
            });
        }

        // 返回true表示异步响应
        return true;
    });

    console.log('📡 消息监听器设置完成');
    console.log('🎯 等待来自popup的指令...');
}

/**
 * 处理ping请求
 */
function handlePing(message, sendResponse) {
    console.log('🏓 处理ping请求');
    sendResponse({
        success: true,
        message: 'Content script连接正常',
        url: window.location.href,
        title: document.title,
        timestamp: Date.now()
    });
}

/**
 * 处理指令执行
 * 增强的指令处理逻辑，支持详细的调试信息和错误处理
 */
function handleCommandExecution(message, sendResponse) {
    const { command, isRealtime, inputMethod, timestamp } = message;

    // 详细的指令执行日志
    const inputIcon = inputMethod === 'voice' ? '🎤' : '⌨️';
    const realtimeIcon = isRealtime ? '⚡' : '👆';

    console.log(`🎯 开始执行指令:`);
    console.log(`   ${inputIcon} 指令内容: "${command}"`);
    console.log(`   ${realtimeIcon} 执行模式: ${isRealtime ? '实时执行' : '手动执行'}`);
    console.log(`   📱 输入方式: ${inputMethod || '未知'}`);
    console.log(`   ⏰ 接收时间: ${new Date(timestamp).toLocaleTimeString()}`);

    try {
        // 避免重复执行相同指令（仅对实时指令生效）
        if (command === lastExecutedCommand && isRealtime) {
            console.log('⏭️ 跳过重复执行的实时指令');
            sendResponse({
                success: true,
                message: '指令已执行，跳过重复执行',
                result: '重复指令已忽略'
            });
            return;
        }

        // 执行指令并获取结果
        const result = executeCommand(command, isRealtime, inputMethod);

        // 记录到指令历史
        const historyEntry = {
            command: command,
            isRealtime: isRealtime,
            inputMethod: inputMethod,
            timestamp: Date.now(),
            result: result,
            executionTime: Date.now() - timestamp
        };

        commandHistory.push(historyEntry);

        // 限制历史记录长度
        if (commandHistory.length > 50) {
            commandHistory = commandHistory.slice(-50);
        }

        // 更新最后执行的指令
        lastExecutedCommand = command;

        // 执行成功日志
        console.log(`✅ 指令执行成功: "${command}"`);
        console.log(`📊 执行结果: ${result}`);
        console.log(`⏱️ 执行耗时: ${Date.now() - timestamp}ms`);

        // 返回成功响应
        sendResponse({
            success: true,
            message: '指令执行成功',
            result: result,
            executionTime: Date.now() - timestamp,
            inputMethod: inputMethod
        });

    } catch (error) {
        // 详细的错误日志
        console.error('❌ 指令执行失败:', {
            command: command,
            error: error.message,
            stack: error.stack,
            inputMethod: inputMethod,
            isRealtime: isRealtime,
            timestamp: timestamp
        });

        // 返回错误响应
        sendResponse({
            success: false,
            error: `指令执行失败: ${error.message}`,
            command: command,
            inputMethod: inputMethod,
            timestamp: Date.now()
        });
    }
}

/**
 * 处理清空操作
 */
function handleClear(message, sendResponse) {
    console.log('🗑️ 处理清空操作');

    try {
        // 清空相关状态
        lastExecutedCommand = '';

        // 移除页面上的指令效果（如果有的话）
        clearPageEffects();

        sendResponse({
            success: true,
            message: '清空操作完成'
        });

    } catch (error) {
        console.error('❌ 清空操作失败:', error);
        sendResponse({
            success: false,
            error: error.message
        });
    }
}

/**
 * 执行具体指令
 * 实现基本的指令解析逻辑，支持语音和键盘输入
 */
function executeCommand(command, isRealtime, inputMethod) {
    if (!command || command.trim().length === 0) {
        console.log('📝 收到空指令，无需执行');
        return '空指令，无需执行';
    }

    const originalCommand = command.trim();
    const trimmedCommand = command.trim().toLowerCase();

    console.log(`🔍 开始解析指令: "${originalCommand}"`);
    console.log(`📋 标准化后: "${trimmedCommand}"`);

    // 使用智能指令解析系统
    const parsedCommand = parseCommandIntelligently(originalCommand, trimmedCommand);

    if (parsedCommand) {
        console.log('🧠 智能解析结果:', parsedCommand);
        return executeIntelligentCommand(parsedCommand);
    }

    // 如果智能解析失败，回退到原有的简单解析
    console.log('⚠️ 智能解析失败，使用传统解析方式');

    // 智能"点击"类指令处理
    if (trimmedCommand.startsWith('点击')) {
        console.log('🖱️ 收到点击类指令:', originalCommand);
        return handleClickCommand(originalCommand, trimmedCommand);
    }

    // 智能"输入"类指令处理
    if (trimmedCommand.includes('输入') || trimmedCommand.startsWith('在') && trimmedCommand.includes('输入')) {
        console.log('⌨️ 收到输入类指令:', originalCommand);
        return handleInputCommand(originalCommand, trimmedCommand);
    }

    // 智能"滚动"类指令处理
    if (trimmedCommand.includes('滚动') || trimmedCommand.startsWith('scroll')) {
        console.log('📜 收到滚动类指令:', originalCommand);
        return handleScrollCommand(originalCommand, trimmedCommand);
    }

    // 智能"标签页"类指令处理
    if (trimmedCommand.includes('标签页') || trimmedCommand.includes('选项卡') ||
        trimmedCommand.includes('tab') || trimmedCommand.includes('切换到') ||
        trimmedCommand.includes('关闭') && (trimmedCommand.includes('第') || trimmedCommand.includes('个')) ||
        trimmedCommand.includes('新建') || trimmedCommand.includes('刷新') || trimmedCommand.includes('复制')) {
        console.log('🔄 收到标签页类指令:', originalCommand);
        return handleTabCommand(originalCommand, trimmedCommand);
    }

    // 预定义的指令处理
    switch (trimmedCommand) {
        case 'hello':
        case '你好':
            console.log('👋 执行欢迎指令');
            return showPageMessage('👋 你好！插件正在正常工作！', 'success');

        case 'scroll down':
        case '向下滚动':
            console.log('📜 执行向下滚动指令');
            window.scrollBy(0, 200);
            return '页面向下滚动200px';

        case 'scroll up':
        case '向上滚动':
            console.log('📜 执行向上滚动指令');
            window.scrollBy(0, -200);
            return '页面向上滚动200px';

        case 'scroll top':
        case '滚动到顶部':
            console.log('📜 执行滚动到顶部指令');
            window.scrollTo(0, 0);
            return '页面滚动到顶部';

        case 'scroll bottom':
        case '滚动到底部':
            console.log('📜 执行滚动到底部指令');
            window.scrollTo(0, document.body.scrollHeight);
            return '页面滚动到底部';

        case 'highlight links':
        case '高亮链接':
            console.log('🔗 执行高亮链接指令');
            return highlightElements('a', '#ffeb3b', '链接');

        case 'highlight images':
        case '高亮图片':
            console.log('🖼️ 执行高亮图片指令');
            return highlightElements('img', '#4caf50', '图片');

        case 'clear highlights':
        case '清除高亮':
            console.log('🧹 执行清除高亮指令');
            return clearHighlights();

        case 'page info':
        case '页面信息':
            console.log('📊 执行页面信息指令');
            return showPageInfo();

        case 'debug on':
        case '开启调试':
            console.log('🔧 开启调试模式');
            debugMode = true;
            createDebugPanel();
            return '调试模式已开启';

        case 'debug off':
        case '关闭调试':
            console.log('🔧 关闭调试模式');
            debugMode = false;
            removeDebugPanel();
            return '调试模式已关闭';

        default:
            // 尝试作为CSS选择器处理
            if (trimmedCommand.startsWith('click ')) {
                const selector = trimmedCommand.substring(6);
                console.log(`🖱️ 执行点击指令: ${selector}`);
                return clickElement(selector);
            }

            if (trimmedCommand.startsWith('hide ')) {
                const selector = trimmedCommand.substring(5);
                console.log(`👻 执行隐藏指令: ${selector}`);
                return hideElements(selector);
            }

            if (trimmedCommand.startsWith('show ')) {
                const selector = trimmedCommand.substring(5);
                console.log(`👁️ 执行显示指令: ${selector}`);
                return showElements(selector);
            }

            // 对于暂未实现的指令，在控制台打印并显示提示
            console.log(`❓ 收到指令: "${originalCommand}"，暂未实现具体功能`);
            console.log('📝 指令详情:', {
                原始指令: originalCommand,
                标准化指令: trimmedCommand,
                输入方式: inputMethod,
                实时模式: isRealtime,
                时间戳: new Date().toLocaleTimeString()
            });

            return showPageMessage(`收到指令: "${originalCommand}"，暂未实现具体功能`, 'info');
    }
}

/**
 * 智能指令解析系统
 * 使用更强大的NLP技术解析自然语言指令
 */
function parseCommandIntelligently(originalCommand, trimmedCommand) {
    console.log('🧠 开始智能指令解析:', originalCommand);

    try {
        // 1. 提取核心动作和目标描述
        const actionInfo = extractActionAndTarget(originalCommand, trimmedCommand);

        if (!actionInfo) {
            console.log('❌ 无法提取动作信息');
            return null;
        }

        console.log('🎯 动作信息提取结果:', actionInfo);

        // 2. 根据动作类型进行详细解析
        let parsedCommand = null;

        switch (actionInfo.action) {
            case 'click':
                parsedCommand = parseClickCommandIntelligently(actionInfo, originalCommand);
                break;
            case 'input':
                parsedCommand = parseInputCommandIntelligently(actionInfo, originalCommand);
                break;
            case 'scroll':
                parsedCommand = parseScrollCommandIntelligently(actionInfo, originalCommand);
                break;
            case 'tab':
                parsedCommand = parseTabCommandIntelligently(actionInfo, originalCommand);
                break;
            default:
                console.log('❌ 未知的动作类型:', actionInfo.action);
                return null;
        }

        if (parsedCommand) {
            parsedCommand.originalCommand = originalCommand;
            parsedCommand.confidence = calculateConfidence(parsedCommand);
        }

        return parsedCommand;

    } catch (error) {
        console.error('❌ 智能解析过程中出错:', error);
        return null;
    }
}

/**
 * 提取核心动作和目标描述
 * 从自然语言指令中识别动作词和目标对象
 */
function extractActionAndTarget(originalCommand, trimmedCommand) {
    console.log('🔍 提取动作和目标:', originalCommand);

    // 定义动作词典
    const actionPatterns = {
        click: [
            // 中文点击动作词
            /(?:请|帮我|帮忙)?(?:点击|点|按|按下|选择|选中|激活)(?:一下|那个|这个)?(.+)/,
            /(.+?)(?:点击|点|按|按下|选择|选中|激活)(?:一下|那个|这个)?/,
            // 中文打开动作词（映射到点击）
            /(?:请|帮我|帮忙)?(?:打开|访问|进入|跳转到?)(?:一下|那个|这个)?(.+)/,
            /(.+?)(?:打开|访问|进入|跳转)(?:一下|那个|这个)?/,
            // 英文点击动作词
            /(?:please|help me|)?(?:click|press|tap|select|activate)(?:on|the)?(.+)/i,
            // 英文打开动作词（映射到点击）
            /(?:please|help me|)?(?:open|visit|go to|navigate to)(?:the)?(.+)/i
        ],
        input: [
            // 中文输入动作词
            /(?:请|帮我|帮忙)?(?:在|把|将)(.+?)(?:输入|填入|填写|写入|录入)(.+?)(?:到|去|里|中)?(.+?)?/,
            /(?:请|帮我|帮忙)?(?:输入|填入|填写|写入|录入)(.+?)(?:到|去|里|中)(.+)/,
            /(?:请|帮我|帮忙)?(?:在)(.+?)(?:输入|填入|填写|写入|录入)(.+)/,
            // 英文输入动作词
            /(?:please|help me|)?(?:input|type|enter|fill)(.+?)(?:into|in|to)(.+)/i
        ],
        scroll: [
            // 中文滚动动作词
            /(?:请|帮我|帮忙)?(?:滚动|翻页|移动)(?:到|至)?(.+)/,
            /(?:请|帮我|帮忙)?(?:向|往)(.+?)(?:滚动|翻页|移动)(.+?)?/,
            // 英文滚动动作词
            /(?:please|help me|)?(?:scroll|move|go)(?:to|down|up)?(.+)/i
        ],
        tab: [
            // 中文标签页操作词
            /(?:请|帮我|帮忙)?(?:切换到|打开|跳转到|转到)(?:第)?(.+?)(?:个)?(?:标签页|选项卡|tab)/,
            /(?:请|帮我|帮忙)?(?:关闭|删除)(?:第)?(.+?)(?:个)?(?:标签页|选项卡|tab)/,
            /(?:请|帮我|帮忙)?(?:新建|创建|打开新的)(?:标签页|选项卡|tab)(?:到|去)?(.+?)?/,
            /(?:请|帮我|帮忙)?(?:刷新|重新加载|重载)(?:第)?(.+?)(?:个)?(?:标签页|选项卡|tab)/,
            /(?:请|帮我|帮忙)?(?:复制|克隆|复制)(?:第)?(.+?)(?:个)?(?:标签页|选项卡|tab)/,
            // 英文标签页操作词
            /(?:please|help me|)?(?:switch to|go to|open)(?:the)?(.+?)(?:tab|page)/i,
            /(?:please|help me|)?(?:close|delete)(?:the)?(.+?)(?:tab|page)/i,
            /(?:please|help me|)?(?:create|new|open new)(?:tab|page)(?:to|for)?(.+?)?/i
        ]
    };

    // 尝试匹配每种动作类型
    for (const [action, patterns] of Object.entries(actionPatterns)) {
        for (const pattern of patterns) {
            const match = trimmedCommand.match(pattern);
            if (match) {
                console.log(`✅ 匹配到${action}动作:`, match);

                const result = {
                    action: action,
                    rawMatch: match,
                    confidence: 0.8
                };

                // 根据动作类型提取具体信息
                switch (action) {
                    case 'click':
                        result.target = extractClickTarget(match, originalCommand);
                        break;
                    case 'input':
                        result.inputInfo = extractInputInfo(match, originalCommand);
                        break;
                    case 'scroll':
                        result.scrollInfo = extractScrollInfo(match, originalCommand);
                        break;
                    case 'tab':
                        result.tabInfo = extractTabInfo(match, originalCommand);
                        break;
                }

                return result;
            }
        }
    }

    console.log('❌ 未匹配到任何已知动作模式');
    return null;
}

/**
 * 提取点击目标信息
 */
function extractClickTarget(match, originalCommand) {
    // 从匹配结果中提取目标描述
    let target = '';

    // 根据匹配组提取目标
    if (match[1]) {
        target = match[1].trim();
    }

    // 清理目标描述
    target = cleanTargetDescription(target);

    console.log('🎯 点击目标提取:', target);

    return {
        description: target,
        keywords: extractTargetKeywords(target),
        modifiers: extractModifiers(target) // 提取修饰词如"红色的"、"大的"等
    };
}

/**
 * 提取输入信息
 */
function extractInputInfo(match, originalCommand) {
    console.log('📝 提取输入信息:', match);

    let content = '';
    let target = '';

    // 根据不同的匹配模式提取内容和目标
    if (match.length >= 3) {
        // 模式: "把XXX输入到YYY" 或 "在XXX输入YYY"
        if (originalCommand.includes('把') || originalCommand.includes('将')) {
            content = match[1]?.trim() || '';
            target = match[3]?.trim() || match[2]?.trim() || '';
        } else if (originalCommand.includes('在')) {
            target = match[1]?.trim() || '';
            content = match[2]?.trim() || '';
        } else {
            // 模式: "输入XXX到YYY"
            content = match[1]?.trim() || '';
            target = match[2]?.trim() || '';
        }
    }

    // 清理内容和目标
    content = cleanInputContent(content);
    target = cleanTargetDescription(target);

    console.log('📝 输入信息提取结果:', { content, target });

    return {
        content: content,
        target: {
            description: target,
            keywords: extractTargetKeywords(target),
            modifiers: extractModifiers(target)
        }
    };
}

/**
 * 提取滚动信息
 */
function extractScrollInfo(match, originalCommand) {
    console.log('📜 提取滚动信息:', match);

    let direction = '';
    let distance = '';

    if (match[1]) {
        const target = match[1].trim();
        direction = target;
    }

    return {
        direction: direction,
        modifiers: extractModifiers(originalCommand)
    };
}

/**
 * 提取标签页操作信息
 */
function extractTabInfo(match, originalCommand) {
    console.log('🔄 提取标签页信息:', match, originalCommand);

    let operation = '';
    let target = '';
    let params = {};

    // 分析原始命令确定操作类型
    const lowerCommand = originalCommand.toLowerCase();

    if (lowerCommand.includes('切换') || lowerCommand.includes('打开') || lowerCommand.includes('跳转') || lowerCommand.includes('转到') || lowerCommand.includes('switch') || lowerCommand.includes('go to')) {
        operation = 'switch_to_tab';
    } else if (lowerCommand.includes('关闭') || lowerCommand.includes('删除') || lowerCommand.includes('close') || lowerCommand.includes('delete')) {
        operation = 'close_tab';
    } else if (lowerCommand.includes('新建') || lowerCommand.includes('创建') || lowerCommand.includes('打开新') || lowerCommand.includes('create') || lowerCommand.includes('new')) {
        operation = 'create_tab';
    } else if (lowerCommand.includes('刷新') || lowerCommand.includes('重新加载') || lowerCommand.includes('重载') || lowerCommand.includes('reload') || lowerCommand.includes('refresh')) {
        operation = 'reload_tab';
    } else if (lowerCommand.includes('复制') || lowerCommand.includes('克隆') || lowerCommand.includes('duplicate') || lowerCommand.includes('copy')) {
        operation = 'duplicate_tab';
    }

    // 提取目标信息
    if (match[1]) {
        target = match[1].trim();
    }

    // 解析目标参数
    if (target) {
        // 检查是否是数字（标签页索引）
        const numberMatch = target.match(/(\d+)/);
        if (numberMatch) {
            params.index = parseInt(numberMatch[1]);
            console.log(`📋 解析到标签页索引: ${params.index}`);
        } else if (target.includes('当前') || target.includes('current') || target.includes('this')) {
            params.current = true;
            console.log('📋 解析到当前标签页操作');
        } else {
            // 可能是标题或URL
            if (target.includes('http') || target.includes('www') || target.includes('.com') || target.includes('.cn')) {
                params.url = target;
                console.log(`📋 解析到URL参数: ${params.url}`);
            } else {
                params.title = target;
                console.log(`📋 解析到标题参数: ${params.title}`);
            }
        }
    }

    // 对于新建标签页，提取URL
    if (operation === 'create_tab' && target) {
        if (target.includes('http') || target.includes('www') || target.includes('.com') || target.includes('.cn')) {
            params.url = target;
        } else {
            // 可能是搜索关键词，构建搜索URL
            params.url = `https://www.google.com/search?q=${encodeURIComponent(target)}`;
        }
    }

    console.log('🔄 标签页信息提取结果:', { operation, target, params });

    return {
        operation: operation,
        target: target,
        params: params
    };
}

/**
 * 清理目标描述
 * 移除无用的词汇和标点符号
 */
function cleanTargetDescription(target) {
    if (!target) return '';

    // 移除常见的无用词汇
    const stopWords = ['那个', '这个', '一下', '的', '了', '吧', '呢', '啊', '呀'];
    let cleaned = target;

    stopWords.forEach(word => {
        cleaned = cleaned.replace(new RegExp(word, 'g'), '');
    });

    // 移除多余的空格和标点符号
    cleaned = cleaned.replace(/[，。！？；：""''（）【】]/g, ' ');
    cleaned = cleaned.replace(/\s+/g, ' ').trim();

    return cleaned;
}

/**
 * 清理输入内容
 * 移除引号和多余的空格
 */
function cleanInputContent(content) {
    if (!content) return '';

    // 移除引号
    let cleaned = content.replace(/["'""'']/g, '');

    // 移除多余的空格
    cleaned = cleaned.replace(/\s+/g, ' ').trim();

    return cleaned;
}

/**
 * 提取目标关键词
 * 从目标描述中提取有用的关键词
 */
function extractTargetKeywords(target) {
    if (!target) return [];

    // 移除常见的修饰词和无用词汇
    const modifierWords = ['红色', '蓝色', '绿色', '黄色', '黑色', '白色', '大', '小', '长', '短', '新', '旧'];
    const stopWords = ['的', '个', '那', '这', '一', '了', '在', '是', '有', '和', '或'];

    let words = target.split(/\s+/);

    // 过滤掉修饰词和停用词
    words = words.filter(word =>
        word.length > 0 &&
        !stopWords.includes(word) &&
        !modifierWords.includes(word)
    );

    return words;
}

/**
 * 提取修饰词
 * 提取颜色、大小等修饰词信息
 */
function extractModifiers(text) {
    if (!text) return {};

    const modifiers = {
        colors: [],
        sizes: [],
        positions: [],
        states: []
    };

    // 颜色修饰词
    const colorWords = ['红色', '蓝色', '绿色', '黄色', '黑色', '白色', '灰色', '紫色', '橙色', '粉色'];
    colorWords.forEach(color => {
        if (text.includes(color)) {
            modifiers.colors.push(color);
        }
    });

    // 大小修饰词
    const sizeWords = ['大', '小', '长', '短', '宽', '窄', '高', '低'];
    sizeWords.forEach(size => {
        if (text.includes(size)) {
            modifiers.sizes.push(size);
        }
    });

    // 位置修饰词
    const positionWords = ['上面', '下面', '左边', '右边', '中间', '顶部', '底部', '左上', '右上', '左下', '右下'];
    positionWords.forEach(position => {
        if (text.includes(position)) {
            modifiers.positions.push(position);
        }
    });

    // 状态修饰词
    const stateWords = ['激活', '禁用', '选中', '未选中', '展开', '折叠', '显示', '隐藏'];
    stateWords.forEach(state => {
        if (text.includes(state)) {
            modifiers.states.push(state);
        }
    });

    return modifiers;
}

/**
 * 智能解析点击指令
 */
function parseClickCommandIntelligently(actionInfo, originalCommand) {
    console.log('🖱️ 智能解析点击指令:', actionInfo);

    return {
        type: 'click',
        target: actionInfo.target,
        originalCommand: originalCommand,
        confidence: actionInfo.confidence
    };
}

/**
 * 智能解析输入指令
 */
function parseInputCommandIntelligently(actionInfo, originalCommand) {
    console.log('⌨️ 智能解析输入指令:', actionInfo);

    return {
        type: 'input',
        content: actionInfo.inputInfo.content,
        target: actionInfo.inputInfo.target,
        originalCommand: originalCommand,
        confidence: actionInfo.confidence
    };
}

/**
 * 智能解析滚动指令
 */
function parseScrollCommandIntelligently(actionInfo, originalCommand) {
    console.log('📜 智能解析滚动指令:', actionInfo);

    return {
        type: 'scroll',
        direction: actionInfo.scrollInfo.direction,
        modifiers: actionInfo.scrollInfo.modifiers,
        originalCommand: originalCommand,
        confidence: actionInfo.confidence
    };
}

/**
 * 智能解析标签页指令
 */
function parseTabCommandIntelligently(actionInfo, originalCommand) {
    console.log('🔄 智能解析标签页指令:', actionInfo);

    return {
        type: 'tab',
        operation: actionInfo.tabInfo.operation,
        target: actionInfo.tabInfo.target,
        params: actionInfo.tabInfo.params,
        originalCommand: originalCommand,
        confidence: actionInfo.confidence
    };
}

/**
 * 执行智能解析的指令
 */
function executeIntelligentCommand(parsedCommand) {
    console.log('🚀 执行智能解析的指令:', parsedCommand);

    try {
        switch (parsedCommand.type) {
            case 'click':
                return executeIntelligentClick(parsedCommand);
            case 'input':
                return executeIntelligentInput(parsedCommand);
            case 'scroll':
                return executeIntelligentScroll(parsedCommand);
            case 'tab':
                return executeIntelligentTab(parsedCommand);
            default:
                throw new Error(`未知的指令类型: ${parsedCommand.type}`);
        }
    } catch (error) {
        console.error('❌ 智能指令执行失败:', error);
        return showPageMessage(`指令执行失败: ${error.message}`, 'error');
    }
}

/**
 * 执行智能点击指令
 */
function executeIntelligentClick(parsedCommand) {
    console.log('🖱️ 执行智能点击指令:', parsedCommand);

    const target = parsedCommand.target;
    const element = findElementIntelligently(target);

    if (!element) {
        const message = `未找到匹配的元素: "${target.description}"`;
        console.warn('⚠️', message);
        return showPageMessage(message, 'warning');
    }

    return performClick(element, target.description);
}

/**
 * 执行智能输入指令
 */
function executeIntelligentInput(parsedCommand) {
    console.log('⌨️ 执行智能输入指令:', parsedCommand);

    const target = parsedCommand.target;
    const content = parsedCommand.content;

    const element = findInputElementIntelligently(target);

    if (!element) {
        const message = `未找到匹配的输入框: "${target.description}"`;
        console.warn('⚠️', message);
        return showPageMessage(message, 'warning');
    }

    return performInput(element, content, target.description);
}

/**
 * 执行智能滚动指令
 */
function executeIntelligentScroll(parsedCommand) {
    console.log('📜 执行智能滚动指令:', parsedCommand);

    // 将智能解析的结果转换为原有的滚动格式
    const scrollInfo = convertToScrollInfo(parsedCommand);
    return performScroll(scrollInfo);
}

/**
 * 执行智能标签页指令
 */
function executeIntelligentTab(parsedCommand) {
    console.log('🔄 执行智能标签页指令:', parsedCommand);

    const { operation, params } = parsedCommand;

    // 发送消息到background script执行标签页操作
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({
            action: 'tab_operation',
            data: {
                operation: operation,
                params: params
            }
        }, (response) => {
            if (response && response.success) {
                const message = response.message || `标签页操作 ${operation} 执行成功`;
                console.log('✅ 标签页操作成功:', message);
                showPageMessage(message, 'success');
                resolve(message);
            } else {
                const errorMessage = response?.error || '标签页操作失败';
                console.error('❌ 标签页操作失败:', errorMessage);
                showPageMessage(`标签页操作失败: ${errorMessage}`, 'error');
                resolve(`标签页操作失败: ${errorMessage}`);
            }
        });
    });
}

/**
 * 计算指令解析的置信度
 */
function calculateConfidence(parsedCommand) {
    let confidence = 0.5; // 基础置信度

    // 根据不同因素调整置信度
    if (parsedCommand.target && parsedCommand.target.keywords.length > 0) {
        confidence += 0.2;
    }

    if (parsedCommand.target && parsedCommand.target.modifiers) {
        const modifierCount = Object.values(parsedCommand.target.modifiers).flat().length;
        confidence += Math.min(modifierCount * 0.1, 0.3);
    }

    return Math.min(confidence, 1.0);
}

/**
 * 智能查找元素
 * 综合考虑文本内容、属性、修饰词等进行元素定位
 */
function findElementIntelligently(target) {
    console.log('🔍 开始智能查找元素...');
    console.log('📋 查找目标详情:', {
        描述: target.description,
        关键词: target.keywords,
        修饰词: target.modifiers
    });

    const { description, keywords, modifiers } = target;

    // 定义可点击元素的选择器
    const clickableSelectors = [
        'button',
        'input[type="submit"]',
        'input[type="button"]',
        'a[href]',
        '[role="button"]',
        '[role="link"]',
        '[onclick]',
        '.btn', '.button',
        '[tabindex]'
    ];

    console.log('🎯 尝试查找包含关键词的可点击元素:', keywords);
    console.log('🔍 搜索选择器:', clickableSelectors);

    let bestElement = null;
    let bestScore = 0;
    let totalElementsChecked = 0;

    try {
        // 遍历所有可点击元素
        for (const selector of clickableSelectors) {
            console.log(`🔍 正在检查选择器: ${selector}`);

            let elements;
            try {
                elements = document.querySelectorAll(selector);
                console.log(`📊 找到 ${elements.length} 个 ${selector} 元素`);
            } catch (error) {
                console.error(`❌ 查询选择器 ${selector} 失败:`, error);
                continue;
            }

            for (const element of elements) {
                totalElementsChecked++;

                // 检查元素可见性
                if (!isElementVisible(element)) {
                    console.log(`👻 跳过不可见元素:`, {
                        标签: element.tagName,
                        文本: element.textContent?.trim().substring(0, 30) || '(无文本)',
                        ID: element.id || '(无ID)',
                        类名: element.className || '(无类名)'
                    });
                    continue;
                }

                console.log(`🔍 检查可见元素:`, {
                    标签: element.tagName,
                    文本: element.textContent?.trim().substring(0, 50) || '(无文本)',
                    ID: element.id || '(无ID)',
                    类名: element.className || '(无类名)',
                    选择器: selector
                });

                try {
                    const score = calculateElementMatchScore(element, keywords, modifiers, description);
                    console.log(`📊 元素匹配分数: ${score.toFixed(2)}`);

                    if (score > bestScore) {
                        bestScore = score;
                        bestElement = element;
                        console.log(`🎯 发现更好的匹配元素! 分数: ${score.toFixed(2)}`);
                    }

                    if (score > 0.6) { // 匹配度阈值
                        console.log('✅ 找到高匹配度元素:', {
                            元素: element.tagName,
                            文本: element.textContent?.trim().substring(0, 50),
                            匹配分数: score.toFixed(2),
                            选择器: selector,
                            ID: element.id || '(无ID)',
                            类名: element.className || '(无类名)'
                        });
                        return element;
                    }
                } catch (error) {
                    console.error('❌ 计算元素匹配分数时出错:', error, element);
                }
            }
        }

        console.log(`📊 搜索完成统计:`, {
            总检查元素数: totalElementsChecked,
            最佳匹配分数: bestScore.toFixed(2),
            是否找到元素: bestElement !== null
        });

        if (bestElement && bestScore > 0.3) {
            console.log('⚠️ 使用最佳匹配元素 (分数较低):', {
                元素: bestElement.tagName,
                文本: bestElement.textContent?.trim().substring(0, 50),
                匹配分数: bestScore.toFixed(2)
            });
            return bestElement;
        }

    } catch (error) {
        console.error('❌ 智能查找元素过程中发生错误:', error);
    }

    console.log('❌ 未找到匹配的可点击元素');
    console.log('💡 建议: 尝试使用更具体的描述或检查页面是否包含目标元素');
    return null;
}

/**
 * 智能查找输入框元素
 */
function findInputElementIntelligently(target) {
    console.log('🔍 开始智能查找输入框元素...');
    console.log('📋 输入框查找目标详情:', {
        描述: target.description,
        关键词: target.keywords,
        修饰词: target.modifiers
    });

    const { description, keywords, modifiers } = target;

    // 定义输入框选择器
    const inputSelectors = [
        'input[type="text"]',
        'input[type="password"]',
        'input[type="email"]',
        'input[type="search"]',
        'input[type="tel"]',
        'input[type="url"]',
        'input[type="number"]',
        'input:not([type])',
        'textarea'
    ];

    console.log('🎯 尝试查找包含关键词的输入框元素:', keywords);
    console.log('🔍 搜索输入框选择器:', inputSelectors);

    let bestElement = null;
    let bestScore = 0;
    let totalInputsChecked = 0;

    try {
        // 遍历所有输入框元素
        for (const selector of inputSelectors) {
            console.log(`🔍 正在检查输入框选择器: ${selector}`);

            let elements;
            try {
                elements = document.querySelectorAll(selector);
                console.log(`📊 找到 ${elements.length} 个 ${selector} 输入框`);
            } catch (error) {
                console.error(`❌ 查询输入框选择器 ${selector} 失败:`, error);
                continue;
            }

            for (const element of elements) {
                totalInputsChecked++;

                // 检查元素可见性
                if (!isElementVisible(element)) {
                    console.log(`👻 跳过不可见输入框:`, {
                        标签: element.tagName,
                        类型: element.type || '(无类型)',
                        占位符: element.placeholder || '(无占位符)',
                        ID: element.id || '(无ID)',
                        name: element.name || '(无name)'
                    });
                    continue;
                }

                console.log(`🔍 检查可见输入框:`, {
                    标签: element.tagName,
                    类型: element.type || '(无类型)',
                    占位符: element.placeholder || '(无占位符)',
                    ID: element.id || '(无ID)',
                    name: element.name || '(无name)',
                    选择器: selector
                });

                try {
                    const score = calculateInputElementMatchScore(element, keywords, modifiers, description);
                    console.log(`📊 输入框匹配分数: ${score.toFixed(2)}`);

                    if (score > bestScore) {
                        bestScore = score;
                        bestElement = element;
                        console.log(`🎯 发现更好的匹配输入框! 分数: ${score.toFixed(2)}`);
                    }

                    if (score > 0.6) { // 匹配度阈值
                        console.log('✅ 找到高匹配度输入框:', {
                            元素: element.tagName,
                            类型: element.type,
                            占位符: element.placeholder,
                            匹配分数: score.toFixed(2),
                            ID: element.id || '(无ID)',
                            name: element.name || '(无name)'
                        });
                        return element;
                    }
                } catch (error) {
                    console.error('❌ 计算输入框匹配分数时出错:', error, element);
                }
            }
        }

        console.log(`📊 输入框搜索完成统计:`, {
            总检查输入框数: totalInputsChecked,
            最佳匹配分数: bestScore.toFixed(2),
            是否找到输入框: bestElement !== null
        });

        if (bestElement && bestScore > 0.3) {
            console.log('⚠️ 使用最佳匹配输入框 (分数较低):', {
                元素: bestElement.tagName,
                类型: bestElement.type,
                占位符: bestElement.placeholder,
                匹配分数: bestScore.toFixed(2)
            });
            return bestElement;
        }

    } catch (error) {
        console.error('❌ 智能查找输入框过程中发生错误:', error);
    }

    console.log('❌ 未找到匹配的输入框元素');
    console.log('💡 建议: 尝试使用更具体的输入框描述或检查页面是否包含目标输入框');
    return null;
}

/**
 * 计算元素匹配分数
 */
function calculateElementMatchScore(element, keywords, modifiers, description) {
    let score = 0;

    // 获取元素的所有文本信息
    const textContent = element.textContent?.toLowerCase() || '';
    const value = element.value?.toLowerCase() || '';
    const title = element.title?.toLowerCase() || '';
    const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';
    const className = element.className?.toLowerCase() || '';
    const id = element.id?.toLowerCase() || '';

    const allText = `${textContent} ${value} ${title} ${ariaLabel} ${className} ${id}`;

    // 关键词匹配
    keywords.forEach(keyword => {
        if (allText.includes(keyword.toLowerCase())) {
            score += 0.3;
        }
    });

    // 完整描述匹配
    if (allText.includes(description.toLowerCase())) {
        score += 0.4;
    }

    // 修饰词匹配
    if (modifiers.colors) {
        modifiers.colors.forEach(color => {
            if (allText.includes(color) || hasColorStyle(element, color)) {
                score += 0.2;
            }
        });
    }

    if (modifiers.sizes) {
        modifiers.sizes.forEach(size => {
            if (allText.includes(size) || hasSizeStyle(element, size)) {
                score += 0.1;
            }
        });
    }

    return Math.min(score, 1.0);
}

/**
 * 计算输入框元素匹配分数
 */
function calculateInputElementMatchScore(element, keywords, modifiers, description) {
    let score = 0;

    // 获取输入框的所有相关信息
    const placeholder = element.placeholder?.toLowerCase() || '';
    const name = element.name?.toLowerCase() || '';
    const id = element.id?.toLowerCase() || '';
    const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';

    // 查找关联的label
    let labelText = '';
    if (element.id) {
        const label = document.querySelector(`label[for="${element.id}"]`);
        labelText = label?.textContent?.toLowerCase() || '';
    }

    const parentLabel = element.closest('label');
    if (parentLabel) {
        labelText += ' ' + (parentLabel.textContent?.toLowerCase() || '');
    }

    const allText = `${placeholder} ${name} ${id} ${ariaLabel} ${labelText}`;

    // 关键词匹配
    keywords.forEach(keyword => {
        if (allText.includes(keyword.toLowerCase())) {
            score += 0.4;
        }
    });

    // 完整描述匹配
    if (allText.includes(description.toLowerCase())) {
        score += 0.5;
    }

    return Math.min(score, 1.0);
}

/**
 * 检查元素是否有指定颜色样式
 */
function hasColorStyle(element, color) {
    const style = window.getComputedStyle(element);
    const backgroundColor = style.backgroundColor;
    const borderColor = style.borderColor;
    const textColor = style.color;

    // 简单的颜色匹配逻辑
    const colorMap = {
        '红色': ['red', 'rgb(255', '#ff', '#f00'],
        '蓝色': ['blue', 'rgb(0, 0, 255)', '#00f', '#0000ff'],
        '绿色': ['green', 'rgb(0, 255, 0)', '#0f0', '#00ff00'],
        '黄色': ['yellow', 'rgb(255, 255, 0)', '#ff0', '#ffff00']
    };

    const colorValues = colorMap[color] || [];
    const allColors = `${backgroundColor} ${borderColor} ${textColor}`.toLowerCase();

    return colorValues.some(colorValue => allColors.includes(colorValue));
}

/**
 * 检查元素是否有指定大小样式
 */
function hasSizeStyle(element, size) {
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);

    // 简单的大小判断逻辑
    switch (size) {
        case '大':
            return rect.width > 100 || rect.height > 40;
        case '小':
            return rect.width < 50 || rect.height < 20;
        default:
            return false;
    }
}

/**
 * 转换为滚动信息格式
 */
function convertToScrollInfo(parsedCommand) {
    const direction = parsedCommand.direction;
    const modifiers = parsedCommand.modifiers;

    // 根据方向和修饰词确定滚动类型
    if (direction.includes('底部') || direction.includes('bottom')) {
        return { type: 'absolute', direction: 'bottom', description: '滚动到页面底部' };
    } else if (direction.includes('顶部') || direction.includes('top')) {
        return { type: 'absolute', direction: 'top', description: '滚动到页面顶部' };
    } else if (direction.includes('中间') || direction.includes('middle')) {
        return { type: 'absolute', direction: 'middle', description: '滚动到页面中间' };
    } else if (direction.includes('下') || direction.includes('down')) {
        let distance = 200;
        if (modifiers.sizes && modifiers.sizes.includes('小')) distance = 100;
        if (modifiers.sizes && modifiers.sizes.includes('大')) distance = 500;
        return { type: 'relative', direction: 'down', distance: distance, speed: 'normal', description: `向下滚动${distance}px` };
    } else if (direction.includes('上') || direction.includes('up')) {
        let distance = 200;
        if (modifiers.sizes && modifiers.sizes.includes('小')) distance = 100;
        if (modifiers.sizes && modifiers.sizes.includes('大')) distance = 500;
        return { type: 'relative', direction: 'up', distance: distance, speed: 'normal', description: `向上滚动${distance}px` };
    }

    // 默认向下滚动
    return { type: 'relative', direction: 'down', distance: 200, speed: 'normal', description: '向下滚动200px' };
}

/**
 * 处理"滚动"类指令
 * 智能识别和处理各种滚动指令
 * 支持格式：
 * - "滚动到底部" / "滚动到顶部"
 * - "向下滚动一点" / "向上滚动一点"
 * - "向下滚动" / "向上滚动"
 * - "滚动到页面中间"
 * - "快速滚动到底部"
 */
function handleScrollCommand(originalCommand, trimmedCommand) {
    console.log('📜 开始处理滚动指令:', originalCommand);

    try {
        // 解析滚动指令
        const scrollInfo = parseScrollCommand(originalCommand, trimmedCommand);

        if (!scrollInfo) {
            const message = '无法解析滚动指令格式';
            console.warn('⚠️', message);
            return showPageMessage(message, 'warning');
        }

        console.log('📝 滚动解析结果:', scrollInfo);

        // 执行滚动操作
        return performScroll(scrollInfo);

    } catch (error) {
        console.error('❌ 滚动指令处理失败:', error);
        return showPageMessage(`滚动指令执行失败: ${error.message}`, 'error');
    }
}

/**
 * 解析滚动指令
 * 支持多种滚动指令格式
 */
function parseScrollCommand(originalCommand, trimmedCommand) {
    console.log('🔍 解析滚动指令格式:', originalCommand);

    // 滚动到特定位置的指令
    if (trimmedCommand.includes('滚动到底部') || trimmedCommand.includes('scroll bottom')) {
        console.log('✅ 匹配: 滚动到底部');
        return {
            type: 'absolute',
            direction: 'bottom',
            description: '滚动到页面底部'
        };
    }

    if (trimmedCommand.includes('滚动到顶部') || trimmedCommand.includes('scroll top')) {
        console.log('✅ 匹配: 滚动到顶部');
        return {
            type: 'absolute',
            direction: 'top',
            description: '滚动到页面顶部'
        };
    }

    if (trimmedCommand.includes('滚动到中间') || trimmedCommand.includes('滚动到页面中间') || trimmedCommand.includes('scroll middle')) {
        console.log('✅ 匹配: 滚动到页面中间');
        return {
            type: 'absolute',
            direction: 'middle',
            description: '滚动到页面中间'
        };
    }

    // 相对滚动指令 - 向下
    if (trimmedCommand.includes('向下滚动') || trimmedCommand.includes('scroll down')) {
        let distance = 200; // 默认滚动距离
        let speed = 'normal'; // 默认滚动速度

        // 检查是否有"一点"、"少量"等修饰词
        if (trimmedCommand.includes('一点') || trimmedCommand.includes('少量') || trimmedCommand.includes('一些')) {
            distance = 100;
        } else if (trimmedCommand.includes('很多') || trimmedCommand.includes('大量') || trimmedCommand.includes('快速')) {
            distance = 500;
            speed = 'fast';
        } else if (trimmedCommand.includes('慢慢') || trimmedCommand.includes('缓慢')) {
            speed = 'slow';
        }

        console.log('✅ 匹配: 向下滚动');
        return {
            type: 'relative',
            direction: 'down',
            distance: distance,
            speed: speed,
            description: `向下滚动${distance}px`
        };
    }

    // 相对滚动指令 - 向上
    if (trimmedCommand.includes('向上滚动') || trimmedCommand.includes('scroll up')) {
        let distance = 200; // 默认滚动距离
        let speed = 'normal'; // 默认滚动速度

        // 检查是否有"一点"、"少量"等修饰词
        if (trimmedCommand.includes('一点') || trimmedCommand.includes('少量') || trimmedCommand.includes('一些')) {
            distance = 100;
        } else if (trimmedCommand.includes('很多') || trimmedCommand.includes('大量') || trimmedCommand.includes('快速')) {
            distance = 500;
            speed = 'fast';
        } else if (trimmedCommand.includes('慢慢') || trimmedCommand.includes('缓慢')) {
            speed = 'slow';
        }

        console.log('✅ 匹配: 向上滚动');
        return {
            type: 'relative',
            direction: 'up',
            distance: distance,
            speed: speed,
            description: `向上滚动${distance}px`
        };
    }

    // 按屏幕高度滚动
    if (trimmedCommand.includes('向下翻页') || trimmedCommand.includes('下一页') || trimmedCommand.includes('page down')) {
        console.log('✅ 匹配: 向下翻页');
        return {
            type: 'page',
            direction: 'down',
            description: '向下翻页'
        };
    }

    if (trimmedCommand.includes('向上翻页') || trimmedCommand.includes('上一页') || trimmedCommand.includes('page up')) {
        console.log('✅ 匹配: 向上翻页');
        return {
            type: 'page',
            direction: 'up',
            description: '向上翻页'
        };
    }

    console.log('❌ 无法匹配任何滚动指令格式');
    return null;
}

/**
 * 执行滚动操作
 * 根据解析结果执行相应的滚动动作
 */
function performScroll(scrollInfo) {
    try {
        console.log('📜 执行滚动操作:', scrollInfo);

        let scrollPromise;

        switch (scrollInfo.type) {
            case 'absolute':
                performAbsoluteScroll(scrollInfo);
                break;
            case 'relative':
                performRelativeScroll(scrollInfo);
                break;
            case 'page':
                performPageScroll(scrollInfo);
                break;
            default:
                throw new Error(`未知的滚动类型: ${scrollInfo.type}`);
        }

        // 显示滚动状态消息
        showPageMessage(scrollInfo.description, 'success');

        return scrollInfo.description;

    } catch (error) {
        console.error('❌ 滚动操作失败:', error);
        const message = `滚动失败: ${error.message}`;
        showPageMessage(message, 'error');
        return message;
    }
}

/**
 * 执行绝对位置滚动
 * 滚动到页面的特定位置（顶部、底部、中间）
 */
function performAbsoluteScroll(scrollInfo) {
    console.log('📍 执行绝对位置滚动:', scrollInfo.direction);

    let targetY = 0;

    switch (scrollInfo.direction) {
        case 'top':
            targetY = 0;
            break;
        case 'bottom':
            targetY = Math.max(
                document.body.scrollHeight,
                document.documentElement.scrollHeight,
                document.body.offsetHeight,
                document.documentElement.offsetHeight,
                document.body.clientHeight,
                document.documentElement.clientHeight
            );
            break;
        case 'middle':
            const maxHeight = Math.max(
                document.body.scrollHeight,
                document.documentElement.scrollHeight
            );
            targetY = maxHeight / 2;
            break;
        default:
            throw new Error(`未知的滚动方向: ${scrollInfo.direction}`);
    }

    console.log('📍 目标滚动位置:', targetY);

    // 使用平滑滚动
    window.scrollTo({
        top: targetY,
        behavior: 'smooth'
    });

    // 记录滚动完成
    setTimeout(() => {
        console.log('✅ 绝对位置滚动完成:', {
            目标位置: targetY,
            当前位置: window.pageYOffset || document.documentElement.scrollTop,
            页面高度: document.body.scrollHeight
        });
    }, 1000);
}

/**
 * 执行相对位置滚动
 * 相对当前位置向上或向下滚动指定距离
 */
function performRelativeScroll(scrollInfo) {
    console.log('📏 执行相对位置滚动:', {
        方向: scrollInfo.direction,
        距离: scrollInfo.distance,
        速度: scrollInfo.speed
    });

    const currentY = window.pageYOffset || document.documentElement.scrollTop;
    let deltaY = scrollInfo.distance;

    // 根据方向调整滚动距离
    if (scrollInfo.direction === 'up') {
        deltaY = -deltaY;
    }

    const targetY = Math.max(0, currentY + deltaY);

    // 根据速度设置滚动行为
    let behavior = 'smooth';
    if (scrollInfo.speed === 'fast') {
        behavior = 'auto'; // 快速滚动使用auto
    }

    console.log('📏 相对滚动参数:', {
        当前位置: currentY,
        滚动距离: deltaY,
        目标位置: targetY,
        滚动行为: behavior
    });

    // 执行滚动
    if (scrollInfo.speed === 'slow') {
        // 慢速滚动使用动画
        performSmoothScroll(currentY, targetY, 1000);
    } else {
        window.scrollTo({
            top: targetY,
            behavior: behavior
        });
    }

    // 记录滚动完成
    setTimeout(() => {
        console.log('✅ 相对位置滚动完成:', {
            起始位置: currentY,
            目标位置: targetY,
            实际位置: window.pageYOffset || document.documentElement.scrollTop
        });
    }, 500);
}

/**
 * 执行翻页滚动
 * 按屏幕高度进行滚动
 */
function performPageScroll(scrollInfo) {
    console.log('📄 执行翻页滚动:', scrollInfo.direction);

    const currentY = window.pageYOffset || document.documentElement.scrollTop;
    const viewportHeight = window.innerHeight;
    let deltaY = viewportHeight * 0.8; // 滚动80%的屏幕高度，保留一些重叠

    // 根据方向调整滚动距离
    if (scrollInfo.direction === 'up') {
        deltaY = -deltaY;
    }

    const targetY = Math.max(0, currentY + deltaY);

    console.log('📄 翻页滚动参数:', {
        当前位置: currentY,
        屏幕高度: viewportHeight,
        滚动距离: deltaY,
        目标位置: targetY
    });

    // 执行平滑滚动
    window.scrollTo({
        top: targetY,
        behavior: 'smooth'
    });

    // 记录滚动完成
    setTimeout(() => {
        console.log('✅ 翻页滚动完成:', {
            起始位置: currentY,
            目标位置: targetY,
            实际位置: window.pageYOffset || document.documentElement.scrollTop
        });
    }, 500);
}

/**
 * 执行平滑滚动动画
 * 用于慢速滚动效果
 */
function performSmoothScroll(startY, targetY, duration) {
    console.log('🎬 执行平滑滚动动画:', {
        起始位置: startY,
        目标位置: targetY,
        持续时间: duration
    });

    const startTime = performance.now();
    const distance = targetY - startY;

    function animateScroll(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数（ease-out）
        const easeOut = 1 - Math.pow(1 - progress, 3);
        const currentY = startY + distance * easeOut;

        window.scrollTo(0, currentY);

        if (progress < 1) {
            requestAnimationFrame(animateScroll);
        } else {
            console.log('✅ 平滑滚动动画完成');
        }
    }

    requestAnimationFrame(animateScroll);
}

/**
 * 处理"输入"类指令
 * 智能识别和处理各种输入指令
 * 支持格式：
 * - "在用户名输入框输入 张三"
 * - "输入密码 123456 到密码框"
 * - "在搜索框输入 Chrome插件"
 */
function handleInputCommand(originalCommand, trimmedCommand) {
    console.log('⌨️ 开始处理输入指令:', originalCommand);

    try {
        // 解析输入指令的不同格式
        const inputInfo = parseInputCommand(originalCommand, trimmedCommand);

        if (!inputInfo) {
            const message = '无法解析输入指令格式';
            console.warn('⚠️', message);
            return showPageMessage(message, 'warning');
        }

        console.log('📝 解析结果:', inputInfo);

        // 查找目标输入框
        const inputElement = findInputElementForInput(inputInfo.targetKeywords);

        if (!inputElement) {
            const message = `未找到包含"${inputInfo.targetKeywords.join(' ')}"的输入框`;
            console.warn('⚠️', message);
            return showPageMessage(message, 'warning');
        }

        // 执行输入操作
        return performInput(inputElement, inputInfo.content, inputInfo.targetKeywords.join(' '));

    } catch (error) {
        console.error('❌ 输入指令处理失败:', error);
        return showPageMessage(`输入指令执行失败: ${error.message}`, 'error');
    }
}

/**
 * 解析输入指令
 * 支持多种输入指令格式
 */
function parseInputCommand(originalCommand, trimmedCommand) {
    console.log('🔍 解析输入指令格式:', originalCommand);

    // 格式1: "在[目标]输入 [内容]"
    const pattern1 = /^在(.+?)输入\s+(.+)$/;
    const match1 = trimmedCommand.match(pattern1);
    if (match1) {
        const target = match1[1].trim();
        const content = match1[2].trim();
        const targetKeywords = extractKeywords(target, ['输入框', '输入', 'input', '框']);

        console.log('✅ 匹配格式1: "在[目标]输入 [内容]"');
        return {
            format: 1,
            target: target,
            content: content,
            targetKeywords: targetKeywords
        };
    }

    // 格式2: "输入[内容] 到 [目标]"
    const pattern2 = /^输入(.+?)\s*到\s*(.+)$/;
    const match2 = trimmedCommand.match(pattern2);
    if (match2) {
        const content = match2[1].trim();
        const target = match2[2].trim();
        const targetKeywords = extractKeywords(target, ['输入框', '输入', 'input', '框']);

        console.log('✅ 匹配格式2: "输入[内容] 到 [目标]"');
        return {
            format: 2,
            target: target,
            content: content,
            targetKeywords: targetKeywords
        };
    }

    // 格式3: "输入[内容]到[目标]" (无空格)
    const pattern3 = /^输入(.+?)到(.+)$/;
    const match3 = trimmedCommand.match(pattern3);
    if (match3) {
        const content = match3[1].trim();
        const target = match3[2].trim();
        const targetKeywords = extractKeywords(target, ['输入框', '输入', 'input', '框']);

        console.log('✅ 匹配格式3: "输入[内容]到[目标]"');
        return {
            format: 3,
            target: target,
            content: content,
            targetKeywords: targetKeywords
        };
    }

    // 格式4: "在[目标]中输入[内容]"
    const pattern4 = /^在(.+?)中输入(.+)$/;
    const match4 = trimmedCommand.match(pattern4);
    if (match4) {
        const target = match4[1].trim();
        const content = match4[2].trim();
        const targetKeywords = extractKeywords(target, ['输入框', '输入', 'input', '框']);

        console.log('✅ 匹配格式4: "在[目标]中输入[内容]"');
        return {
            format: 4,
            target: target,
            content: content,
            targetKeywords: targetKeywords
        };
    }

    // 格式5: 简单的"输入 [内容]" - 尝试找到当前聚焦的输入框
    const pattern5 = /^输入\s+(.+)$/;
    const match5 = trimmedCommand.match(pattern5);
    if (match5) {
        const content = match5[1].trim();

        console.log('✅ 匹配格式5: "输入 [内容]" (使用当前聚焦的输入框)');
        return {
            format: 5,
            target: '当前聚焦的输入框',
            content: content,
            targetKeywords: [] // 空数组表示使用当前聚焦的元素
        };
    }

    console.log('❌ 无法匹配任何输入指令格式');
    return null;
}

/**
 * 查找用于输入的输入框元素
 * 复用并增强现有的findInputElement函数
 */
function findInputElementForInput(keywords) {
    // 如果关键词为空，尝试使用当前聚焦的元素
    if (keywords.length === 0) {
        const activeElement = document.activeElement;
        if (activeElement && isInputElement(activeElement) && isElementVisible(activeElement)) {
            console.log('✅ 使用当前聚焦的输入框:', {
                元素: activeElement.tagName,
                类型: activeElement.type,
                占位符: activeElement.placeholder || ''
            });
            return activeElement;
        }

        console.log('❌ 当前没有聚焦的输入框，尝试查找第一个可见的输入框');
        // 如果没有聚焦的输入框，查找第一个可见的输入框
        const firstInput = document.querySelector('input[type="text"], input[type="search"], input:not([type]), textarea');
        if (firstInput && isElementVisible(firstInput)) {
            console.log('✅ 使用第一个可见的输入框');
            return firstInput;
        }

        return null;
    }

    // 使用关键词查找输入框（复用现有函数）
    return findInputElement(keywords);
}

/**
 * 检查元素是否为输入元素
 */
function isInputElement(element) {
    if (!element) return false;

    const tagName = element.tagName.toLowerCase();
    if (tagName === 'textarea') return true;
    if (tagName === 'input') {
        const type = element.type?.toLowerCase() || 'text';
        return ['text', 'password', 'email', 'search', 'tel', 'url', 'number'].includes(type);
    }

    return false;
}

/**
 * 处理"点击"类指令
 * 智能识别和处理各种点击指令
 */
function handleClickCommand(originalCommand, trimmedCommand) {
    console.log('🎯 开始处理点击指令:', originalCommand);

    try {
        // 移除"点击"前缀，获取目标描述
        const target = trimmedCommand.substring(2).trim(); // 移除"点击"
        console.log('🔍 目标元素描述:', target);

        // 根据不同的目标类型进行处理
        if (target.includes('按钮') || target.includes('button')) {
            return handleButtonClick(target, originalCommand);
        } else if (target.includes('输入框') || target.includes('输入') || target.includes('input')) {
            return handleInputClick(target, originalCommand);
        } else if (target.includes('链接') || target.includes('link') ||
                   target.includes('超链接') || target.includes('网址') ||
                   target.includes('地址') || target.includes('url')) {
            return handleLinkClick(target, originalCommand);
        } else {
            // 通用元素点击处理（包含对链接的智能识别）
            return handleGenericClick(target, originalCommand);
        }

    } catch (error) {
        console.error('❌ 点击指令处理失败:', error);
        return showPageMessage(`点击指令执行失败: ${error.message}`, 'error');
    }
}

/**
 * 处理按钮点击指令
 * 例如："点击登录按钮"、"点击提交按钮"
 */
function handleButtonClick(target, originalCommand) {
    console.log('🔘 处理按钮点击:', target);

    // 提取按钮关键词
    const buttonKeywords = extractKeywords(target, ['按钮', 'button']);
    console.log('🔑 按钮关键词:', buttonKeywords);

    // 查找按钮元素
    const buttonElement = findButtonElement(buttonKeywords);

    if (buttonElement) {
        return performClick(buttonElement, `按钮: ${buttonKeywords.join(' ')}`);
    } else {
        const message = `未找到包含"${buttonKeywords.join(' ')}"的按钮`;
        console.warn('⚠️', message);
        return showPageMessage(message, 'warning');
    }
}

/**
 * 处理输入框点击指令
 * 例如："点击用户名输入框"、"点击密码输入框"
 */
function handleInputClick(target, originalCommand) {
    console.log('📝 处理输入框点击:', target);

    // 提取输入框关键词
    const inputKeywords = extractKeywords(target, ['输入框', '输入', 'input']);
    console.log('🔑 输入框关键词:', inputKeywords);

    // 查找输入框元素
    const inputElement = findInputElement(inputKeywords);

    if (inputElement) {
        // 对于输入框，使其获得焦点而不是点击
        return performFocus(inputElement, `输入框: ${inputKeywords.join(' ')}`);
    } else {
        const message = `未找到包含"${inputKeywords.join(' ')}"的输入框`;
        console.warn('⚠️', message);
        return showPageMessage(message, 'warning');
    }
}

/**
 * 处理链接点击指令
 * 例如："点击首页链接"、"点击帮助链接"
 */
function handleLinkClick(target, originalCommand) {
    console.log('🔗 处理链接点击:', target);

    // 提取链接关键词
    const linkKeywords = extractKeywords(target, ['链接', 'link']);
    console.log('🔑 链接关键词:', linkKeywords);

    // 查找链接元素
    const linkElement = findLinkElement(linkKeywords);

    if (linkElement) {
        return performClick(linkElement, `链接: ${linkKeywords.join(' ')}`);
    } else {
        const message = `未找到包含"${linkKeywords.join(' ')}"的链接`;
        console.warn('⚠️', message);
        return showPageMessage(message, 'warning');
    }
}

/**
 * 处理通用元素点击指令
 * 当无法确定具体元素类型时使用
 */
function handleGenericClick(target, originalCommand) {
    console.log('🎯 处理通用元素点击:', target);

    // 提取所有关键词
    const keywords = target.split(/\s+/).filter(word => word.length > 0);
    console.log('🔑 通用关键词:', keywords);

    // 尝试查找任何包含关键词的可点击元素
    const element = findClickableElement(keywords);

    if (element) {
        return performClick(element, `元素: ${keywords.join(' ')}`);
    } else {
        const message = `未找到包含"${keywords.join(' ')}"的可点击元素`;
        console.warn('⚠️', message);
        return showPageMessage(message, 'warning');
    }
}

/**
 * 提取关键词
 * 从目标描述中提取有用的关键词，排除指定的无用词汇
 */
function extractKeywords(target, excludeWords = []) {
    // 移除标点符号和特殊字符，分割成词汇
    const words = target.replace(/[^\w\s\u4e00-\u9fa5]/g, ' ')
                       .split(/\s+/)
                       .filter(word => word.length > 0);

    // 排除指定的无用词汇
    const keywords = words.filter(word => !excludeWords.includes(word));

    console.log('📝 关键词提取:', {
        原始文本: target,
        排除词汇: excludeWords,
        提取结果: keywords
    });

    return keywords;
}

/**
 * 检查元素是否可见
 * 优先查找可见元素，包含详细的可见性检查日志
 */
function isElementVisible(element, enableDetailedLog = false) {
    if (enableDetailedLog) {
        console.log('👁️ 开始检查元素可见性...');
    }

    try {
        // 基础检查：元素是否存在
        if (!element) {
            if (enableDetailedLog) {
                console.log('❌ 元素不存在 (null/undefined)');
            }
            return false;
        }

        if (enableDetailedLog) {
            console.log('📋 元素基本信息:', {
                标签: element.tagName,
                ID: element.id || '(无ID)',
                类名: element.className || '(无类名)',
                文本: element.textContent?.trim().substring(0, 30) || '(无文本)'
            });
        }

        // 获取计算样式
        let style;
        try {
            style = window.getComputedStyle(element);
            if (enableDetailedLog) {
                console.log('✅ 成功获取元素计算样式');
            }
        } catch (styleError) {
            console.error('❌ 获取元素样式失败:', styleError);
            return false;
        }

        // 获取元素位置信息
        let rect;
        try {
            rect = element.getBoundingClientRect();
            if (enableDetailedLog) {
                console.log('✅ 成功获取元素位置信息');
            }
        } catch (rectError) {
            console.error('❌ 获取元素位置失败:', rectError);
            return false;
        }

        if (enableDetailedLog) {
            console.log('📊 元素样式信息:', {
                display: style.display,
                visibility: style.visibility,
                opacity: style.opacity,
                position: style.position,
                zIndex: style.zIndex
            });

            console.log('📐 元素位置信息:', {
                width: rect.width,
                height: rect.height,
                top: rect.top,
                bottom: rect.bottom,
                left: rect.left,
                right: rect.right,
                视窗高度: window.innerHeight,
                视窗宽度: window.innerWidth
            });
        }

        // 检查display属性
        if (style.display === 'none') {
            if (enableDetailedLog) {
                console.log('❌ 元素不可见: display = none');
            }
            return false;
        }

        // 检查visibility属性
        if (style.visibility === 'hidden') {
            if (enableDetailedLog) {
                console.log('❌ 元素不可见: visibility = hidden');
            }
            return false;
        }

        // 检查opacity属性
        if (style.opacity === '0') {
            if (enableDetailedLog) {
                console.log('❌ 元素不可见: opacity = 0');
            }
            return false;
        }

        // 检查元素尺寸
        if (rect.width <= 0) {
            if (enableDetailedLog) {
                console.log('❌ 元素不可见: width ≤ 0');
            }
            return false;
        }

        if (rect.height <= 0) {
            if (enableDetailedLog) {
                console.log('❌ 元素不可见: height ≤ 0');
            }
            return false;
        }

        // 检查元素是否在视窗内
        if (rect.top >= window.innerHeight) {
            if (enableDetailedLog) {
                console.log('❌ 元素不可见: 位于视窗下方');
            }
            return false;
        }

        if (rect.bottom <= 0) {
            if (enableDetailedLog) {
                console.log('❌ 元素不可见: 位于视窗上方');
            }
            return false;
        }

        if (rect.left >= window.innerWidth) {
            if (enableDetailedLog) {
                console.log('❌ 元素不可见: 位于视窗右侧');
            }
            return false;
        }

        if (rect.right <= 0) {
            if (enableDetailedLog) {
                console.log('❌ 元素不可见: 位于视窗左侧');
            }
            return false;
        }

        // 检查元素是否被其他元素遮挡（可选的高级检查）
        if (enableDetailedLog) {
            try {
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                const elementAtPoint = document.elementFromPoint(centerX, centerY);

                if (elementAtPoint && elementAtPoint !== element && !element.contains(elementAtPoint)) {
                    console.log('⚠️ 元素可能被其他元素遮挡:', {
                        目标元素: element.tagName,
                        遮挡元素: elementAtPoint.tagName,
                        检查点: { x: centerX, y: centerY }
                    });
                } else {
                    console.log('✅ 元素中心点未被遮挡');
                }
            } catch (pointError) {
                console.warn('⚠️ 无法检查元素遮挡情况:', pointError);
            }
        }

        if (enableDetailedLog) {
            console.log('✅ 元素可见性检查通过');
        }

        return true;

    } catch (error) {
        console.error('❌ 检查元素可见性时发生错误:', error);
        return false;
    }
}

/**
 * 查找按钮元素
 * 查找包含指定关键词的按钮元素
 */
function findButtonElement(keywords) {
    console.log('🔍 查找按钮元素，关键词:', keywords);

    // 定义按钮选择器
    const buttonSelectors = [
        'button',
        'input[type="submit"]',
        'input[type="button"]',
        '[role="button"]',
        'a[href]', // 有时链接也用作按钮
        '.btn', '.button', // 常见的按钮类名
        '[onclick]' // 有点击事件的元素
    ];

    // 遍历所有可能的按钮元素
    for (const selector of buttonSelectors) {
        const elements = document.querySelectorAll(selector);

        for (const element of elements) {
            if (!isElementVisible(element)) continue;

            // 检查元素文本内容
            const textContent = element.textContent?.toLowerCase() || '';
            const value = element.value?.toLowerCase() || '';
            const title = element.title?.toLowerCase() || '';
            const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';

            // 检查是否包含任何关键词
            const allText = `${textContent} ${value} ${title} ${ariaLabel}`;
            const hasKeyword = keywords.some(keyword =>
                allText.includes(keyword.toLowerCase())
            );

            if (hasKeyword) {
                console.log('✅ 找到匹配的按钮:', {
                    元素: element.tagName,
                    文本: textContent.trim(),
                    选择器: selector,
                    关键词: keywords
                });
                return element;
            }
        }
    }

    console.log('❌ 未找到匹配的按钮元素');
    return null;
}

/**
 * 查找输入框元素
 * 查找包含指定关键词的输入框元素
 */
function findInputElement(keywords) {
    console.log('🔍 查找输入框元素，关键词:', keywords);

    // 定义输入框选择器
    const inputSelectors = [
        'input[type="text"]',
        'input[type="password"]',
        'input[type="email"]',
        'input[type="search"]',
        'input[type="tel"]',
        'input[type="url"]',
        'input:not([type])', // 默认type为text
        'textarea'
    ];

    // 遍历所有可能的输入框元素
    for (const selector of inputSelectors) {
        const elements = document.querySelectorAll(selector);

        for (const element of elements) {
            if (!isElementVisible(element)) continue;

            // 检查各种属性
            const placeholder = element.placeholder?.toLowerCase() || '';
            const name = element.name?.toLowerCase() || '';
            const id = element.id?.toLowerCase() || '';
            const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';

            // 查找关联的label
            let labelText = '';
            if (element.id) {
                const label = document.querySelector(`label[for="${element.id}"]`);
                labelText = label?.textContent?.toLowerCase() || '';
            }

            // 查找父级label
            const parentLabel = element.closest('label');
            if (parentLabel) {
                labelText += ' ' + (parentLabel.textContent?.toLowerCase() || '');
            }

            // 检查是否包含任何关键词
            const allText = `${placeholder} ${name} ${id} ${ariaLabel} ${labelText}`;
            const hasKeyword = keywords.some(keyword =>
                allText.includes(keyword.toLowerCase())
            );

            if (hasKeyword) {
                console.log('✅ 找到匹配的输入框:', {
                    元素: element.tagName,
                    类型: element.type,
                    占位符: placeholder,
                    标签: labelText.trim(),
                    关键词: keywords
                });
                return element;
            }
        }
    }

    console.log('❌ 未找到匹配的输入框元素');
    return null;
}

/**
 * 查找链接元素
 * 查找包含指定关键词的链接元素，支持多种匹配方式
 */
function findLinkElement(keywords) {
    console.log('🔍 查找链接元素，关键词:', keywords);

    // 定义链接选择器，按优先级排序
    const linkSelectors = [
        'a[href]',           // 标准链接
        '[role="link"]',     // ARIA链接
        'area[href]',        // 图像映射链接
        'link[href]'         // 文档链接（较少用于点击）
    ];

    let bestElement = null;
    let bestScore = 0;

    // 遍历所有可能的链接元素
    for (const selector of linkSelectors) {
        console.log(`🔍 正在检查链接选择器: ${selector}`);

        let elements;
        try {
            elements = document.querySelectorAll(selector);
            console.log(`📊 找到 ${elements.length} 个 ${selector} 元素`);
        } catch (error) {
            console.error(`❌ 查询选择器 ${selector} 失败:`, error);
            continue;
        }

        for (const element of elements) {
            if (!isElementVisible(element)) {
                console.log(`👻 跳过不可见链接:`, {
                    标签: element.tagName,
                    文本: element.textContent?.trim().substring(0, 30) || '(无文本)',
                    链接: element.href || '(无链接)'
                });
                continue;
            }

            // 计算链接匹配分数
            const score = calculateLinkMatchScore(element, keywords);
            console.log(`🔍 检查可见链接:`, {
                标签: element.tagName,
                文本: element.textContent?.trim().substring(0, 50) || '(无文本)',
                链接: element.href || '(无链接)',
                匹配分数: score.toFixed(2),
                选择器: selector
            });

            if (score > bestScore) {
                bestScore = score;
                bestElement = element;
                console.log(`🎯 发现更好的链接匹配! 分数: ${score.toFixed(2)}`);
            }

            // 如果找到高匹配度的链接，直接返回
            if (score > 0.8) {
                console.log('✅ 找到高匹配度链接:', {
                    元素: element.tagName,
                    文本: element.textContent?.trim().substring(0, 50),
                    链接: element.href,
                    匹配分数: score.toFixed(2),
                    关键词: keywords
                });
                return element;
            }
        }
    }

    // 如果有较好的匹配，返回最佳匹配
    if (bestElement && bestScore > 0.3) {
        console.log('⚠️ 使用最佳匹配链接 (分数较低):', {
            元素: bestElement.tagName,
            文本: bestElement.textContent?.trim().substring(0, 50),
            链接: bestElement.href,
            匹配分数: bestScore.toFixed(2),
            关键词: keywords
        });
        return bestElement;
    }

    console.log('❌ 未找到匹配的链接元素');
    console.log('💡 建议: 尝试使用更具体的链接文本或检查页面是否包含目标链接');
    return null;
}

/**
 * 计算链接元素匹配分数
 * 综合考虑文本内容、href、title、aria-label等属性
 */
function calculateLinkMatchScore(element, keywords) {
    let score = 0;
    const keywordCount = keywords.length;

    if (keywordCount === 0) return 0;

    // 获取链接的所有相关信息
    const textContent = element.textContent?.toLowerCase().trim() || '';
    const href = element.href?.toLowerCase() || '';
    const title = element.title?.toLowerCase() || '';
    const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';
    const className = element.className?.toLowerCase() || '';
    const id = element.id?.toLowerCase() || '';

    // 1. 文本内容匹配（权重最高）
    let textMatches = 0;
    keywords.forEach(keyword => {
        const lowerKeyword = keyword.toLowerCase();
        if (textContent.includes(lowerKeyword)) {
            textMatches++;
            // 完全匹配给更高分数
            if (textContent === lowerKeyword) {
                score += 0.4;
            } else if (textContent.startsWith(lowerKeyword) || textContent.endsWith(lowerKeyword)) {
                score += 0.3;
            } else {
                score += 0.2;
            }
        }
    });

    // 2. href属性匹配
    let hrefMatches = 0;
    keywords.forEach(keyword => {
        const lowerKeyword = keyword.toLowerCase();
        if (href.includes(lowerKeyword)) {
            hrefMatches++;
            score += 0.15;
        }
    });

    // 3. title属性匹配
    let titleMatches = 0;
    keywords.forEach(keyword => {
        const lowerKeyword = keyword.toLowerCase();
        if (title.includes(lowerKeyword)) {
            titleMatches++;
            score += 0.1;
        }
    });

    // 4. aria-label属性匹配
    let ariaMatches = 0;
    keywords.forEach(keyword => {
        const lowerKeyword = keyword.toLowerCase();
        if (ariaLabel.includes(lowerKeyword)) {
            ariaMatches++;
            score += 0.1;
        }
    });

    // 5. class和id匹配（较低权重）
    keywords.forEach(keyword => {
        const lowerKeyword = keyword.toLowerCase();
        if (className.includes(lowerKeyword)) {
            score += 0.05;
        }
        if (id.includes(lowerKeyword)) {
            score += 0.05;
        }
    });

    // 6. 链接类型加分
    if (element.tagName.toLowerCase() === 'a' && element.href) {
        score += 0.1; // 标准链接加分
    }

    // 7. 可访问性加分
    if (ariaLabel || title) {
        score += 0.05; // 有可访问性属性的链接加分
    }

    // 记录详细的匹配信息
    console.log(`📊 链接匹配详情:`, {
        文本匹配: `${textMatches}/${keywordCount}`,
        href匹配: `${hrefMatches}/${keywordCount}`,
        title匹配: `${titleMatches}/${keywordCount}`,
        aria匹配: `${ariaMatches}/${keywordCount}`,
        总分: score.toFixed(2),
        文本内容: textContent.substring(0, 30),
        链接地址: href.substring(0, 50)
    });

    return Math.min(score, 1.0); // 确保分数不超过1.0
}

/**
 * 查找可点击元素
 * 查找任何包含关键词的可点击元素，优化对a标签的处理
 */
function findClickableElement(keywords) {
    console.log('🔍 查找可点击元素，关键词:', keywords);

    // 定义可点击元素选择器，按优先级排序
    const clickableSelectors = [
        'button',                    // 按钮优先级最高
        'input[type="submit"]',      // 提交按钮
        'input[type="button"]',      // 普通按钮
        'a[href]',                   // 链接（提高优先级）
        '[role="button"]',           // ARIA按钮
        '[role="link"]',             // ARIA链接
        '[onclick]',                 // 有点击事件的元素
        '.btn', '.button',           // 按钮样式类
        '[tabindex]',                // 可聚焦元素
        'select',                    // 选择框
        'option'                     // 选项
    ];

    let bestElement = null;
    let bestScore = 0;

    // 遍历所有可能的可点击元素
    for (const selector of clickableSelectors) {
        console.log(`🔍 正在检查可点击选择器: ${selector}`);

        let elements;
        try {
            elements = document.querySelectorAll(selector);
            console.log(`📊 找到 ${elements.length} 个 ${selector} 元素`);
        } catch (error) {
            console.error(`❌ 查询选择器 ${selector} 失败:`, error);
            continue;
        }

        for (const element of elements) {
            if (!isElementVisible(element)) {
                console.log(`👻 跳过不可见可点击元素:`, {
                    标签: element.tagName,
                    文本: element.textContent?.trim().substring(0, 30) || '(无文本)',
                    选择器: selector
                });
                continue;
            }

            // 计算元素匹配分数
            let score = 0;
            const isLink = element.tagName.toLowerCase() === 'a';

            // 获取元素的各种文本内容和属性
            const textContent = element.textContent?.toLowerCase() || '';
            const value = element.value?.toLowerCase() || '';
            const title = element.title?.toLowerCase() || '';
            const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';
            const placeholder = element.placeholder?.toLowerCase() || '';

            // 对于链接，还要检查href属性
            const href = isLink ? (element.href?.toLowerCase() || '') : '';
            const className = element.className?.toLowerCase() || '';
            const id = element.id?.toLowerCase() || '';

            // 构建搜索文本
            let allText = `${textContent} ${value} ${title} ${ariaLabel} ${placeholder}`;
            if (isLink) {
                allText += ` ${href} ${className} ${id}`;
            }

            // 计算关键词匹配分数
            let matchCount = 0;
            keywords.forEach(keyword => {
                const lowerKeyword = keyword.toLowerCase();

                // 文本内容匹配（最高权重）
                if (textContent.includes(lowerKeyword)) {
                    matchCount++;
                    if (textContent === lowerKeyword) {
                        score += 0.5; // 完全匹配
                    } else if (textContent.startsWith(lowerKeyword) || textContent.endsWith(lowerKeyword)) {
                        score += 0.3; // 开头或结尾匹配
                    } else {
                        score += 0.2; // 包含匹配
                    }
                }

                // 对于链接，href匹配也很重要
                if (isLink && href.includes(lowerKeyword)) {
                    matchCount++;
                    score += 0.15;
                }

                // 其他属性匹配
                if (title.includes(lowerKeyword)) {
                    score += 0.1;
                }
                if (ariaLabel.includes(lowerKeyword)) {
                    score += 0.1;
                }
                if (value.includes(lowerKeyword)) {
                    score += 0.1;
                }
                if (placeholder.includes(lowerKeyword)) {
                    score += 0.05;
                }
            });

            // 元素类型加分
            if (isLink && element.href && element.href !== '#') {
                score += 0.1; // 有效链接加分
            }
            if (element.tagName.toLowerCase() === 'button') {
                score += 0.1; // 按钮元素加分
            }

            console.log(`🔍 检查可见可点击元素:`, {
                标签: element.tagName,
                文本: textContent.substring(0, 50) || '(无文本)',
                匹配关键词数: `${matchCount}/${keywords.length}`,
                匹配分数: score.toFixed(2),
                选择器: selector,
                是否为链接: isLink,
                链接地址: isLink ? (element.href || '(无链接)') : '(非链接)'
            });

            if (score > bestScore) {
                bestScore = score;
                bestElement = element;
                console.log(`🎯 发现更好的可点击元素匹配! 分数: ${score.toFixed(2)}`);
            }

            // 如果找到高匹配度的元素，直接返回
            if (score > 0.6) {
                console.log('✅ 找到高匹配度可点击元素:', {
                    元素: element.tagName,
                    文本: textContent.trim().substring(0, 50),
                    匹配分数: score.toFixed(2),
                    选择器: selector,
                    关键词: keywords,
                    是否为链接: isLink,
                    链接地址: isLink ? element.href : '(非链接)'
                });
                return element;
            }
        }
    }

    // 如果有较好的匹配，返回最佳匹配
    if (bestElement && bestScore > 0.2) {
        const isLink = bestElement.tagName.toLowerCase() === 'a';
        console.log('⚠️ 使用最佳匹配可点击元素 (分数较低):', {
            元素: bestElement.tagName,
            文本: bestElement.textContent?.trim().substring(0, 50),
            匹配分数: bestScore.toFixed(2),
            关键词: keywords,
            是否为链接: isLink,
            链接地址: isLink ? bestElement.href : '(非链接)'
        });
        return bestElement;
    }

    console.log('❌ 未找到匹配的可点击元素');
    console.log('💡 建议: 尝试使用更具体的描述或检查页面是否包含目标元素');
    return null;
}

/**
 * 执行点击操作
 * 模拟点击指定元素
 */
function performClick(element, description) {
    console.log('🖱️ 准备执行点击操作...');

    // 检查是否为链接元素，添加特殊的调试信息
    const isLink = element.tagName.toLowerCase() === 'a';
    const linkInfo = isLink ? {
        链接地址: element.href || '(无链接地址)',
        链接目标: element.target || '(当前窗口)',
        链接标题: element.title || '(无标题)',
        链接关系: element.rel || '(无关系)',
        下载属性: element.download || '(非下载链接)'
    } : {};

    console.log('📋 点击目标详情:', {
        描述: description,
        元素标签: element.tagName,
        元素ID: element.id || '(无ID)',
        元素类名: element.className || '(无类名)',
        元素文本: element.textContent?.trim().substring(0, 50) || '(无文本)',
        元素类型: element.type || '(无类型)',
        元素位置: element.getBoundingClientRect(),
        是否为链接: isLink,
        ...linkInfo
    });

    // 如果是链接，输出额外的链接相关信息
    if (isLink) {
        console.log('🔗 链接元素特殊信息:', {
            完整链接地址: element.href,
            链接协议: element.protocol || '(未知)',
            链接主机: element.hostname || '(未知)',
            链接路径: element.pathname || '(未知)',
            链接参数: element.search || '(无参数)',
            链接锚点: element.hash || '(无锚点)',
            是否外部链接: element.hostname !== window.location.hostname,
            是否新窗口打开: element.target === '_blank'
        });
    }

    try {
        // 检查元素是否可点击
        console.log('🔍 检查元素可点击性...');
        if (!element) {
            throw new Error('元素为null或undefined');
        }

        if (!isElementVisible(element)) {
            throw new Error('元素不可见');
        }

        // 检查元素是否被禁用
        if (element.disabled) {
            console.warn('⚠️ 元素被禁用，但仍尝试点击');
        }

        console.log('✅ 元素检查通过，开始执行点击操作');

        // 滚动到元素可见位置
        console.log('📜 滚动到元素位置...');
        try {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            console.log('✅ 滚动完成');
        } catch (scrollError) {
            console.warn('⚠️ 滚动失败，但继续执行点击:', scrollError);
        }

        // 高亮显示要点击的元素
        console.log('🎨 高亮显示目标元素...');
        const originalOutline = element.style.outline;
        const originalBackgroundColor = element.style.backgroundColor;

        try {
            element.style.outline = '3px solid #ff4444';
            element.style.backgroundColor = '#ffebee';
            console.log('✅ 元素高亮完成');
        } catch (styleError) {
            console.warn('⚠️ 设置高亮样式失败:', styleError);
        }

        // 延迟执行点击，让用户看到高亮效果
        console.log('⏱️ 延迟0.5秒后执行点击...');
        setTimeout(() => {
            try {
                console.log('🖱️ 开始执行点击事件...');

                // 对于链接元素，添加特殊的点击处理
                if (isLink) {
                    console.log('🔗 执行链接特殊点击处理...');

                    // 检查链接是否有效
                    if (!element.href || element.href === '#' || element.href === 'javascript:void(0)') {
                        console.warn('⚠️ 链接地址无效或为空，但仍尝试点击');
                    } else {
                        console.log('✅ 链接地址有效，准备执行跳转');
                    }

                    // 如果是外部链接且没有设置target，警告用户
                    if (element.hostname !== window.location.hostname && !element.target) {
                        console.warn('⚠️ 外部链接将在当前窗口打开，可能会离开当前页面');
                    }
                }

                // 触发多种点击事件以确保兼容性
                const events = ['mousedown', 'mouseup', 'click'];
                events.forEach(eventType => {
                    try {
                        const event = new MouseEvent(eventType, {
                            bubbles: true,
                            cancelable: true,
                            view: window,
                            // 对于链接，添加额外的事件属性
                            ...(isLink && {
                                ctrlKey: false,
                                metaKey: false,
                                shiftKey: false,
                                altKey: false
                            })
                        });
                        element.dispatchEvent(event);
                        console.log(`✅ ${eventType} 事件触发成功`);
                    } catch (eventError) {
                        console.warn(`⚠️ ${eventType} 事件触发失败:`, eventError);
                    }
                });

                // 执行原生点击
                try {
                    element.click();
                    console.log('✅ 原生click()方法执行成功');

                    // 对于链接，添加额外的成功信息
                    if (isLink) {
                        console.log('🔗 链接点击完成，页面可能即将跳转');
                        if (element.target === '_blank') {
                            console.log('🆕 链接将在新窗口/标签页中打开');
                        } else if (element.href.startsWith('#')) {
                            console.log('⚓ 页面内锚点跳转');
                        } else if (element.hostname === window.location.hostname) {
                            console.log('🏠 站内链接跳转');
                        } else {
                            console.log('🌐 外部链接跳转');
                        }
                    }
                } catch (clickError) {
                    console.warn('⚠️ 原生click()方法失败:', clickError);
                }

                // 恢复原始样式
                try {
                    element.style.outline = originalOutline;
                    element.style.backgroundColor = originalBackgroundColor;
                    console.log('✅ 元素样式已恢复');
                } catch (restoreError) {
                    console.warn('⚠️ 恢复样式失败:', restoreError);
                }

                console.log('✅ 点击操作完成:', {
                    元素: element.tagName,
                    文本: element.textContent?.trim() || element.value || '(无文本)',
                    位置: element.getBoundingClientRect(),
                    时间: new Date().toLocaleTimeString()
                });

            } catch (clickExecutionError) {
                console.error('❌ 点击执行过程中发生错误:', clickExecutionError);

                // 尝试恢复样式
                try {
                    element.style.outline = originalOutline;
                    element.style.backgroundColor = originalBackgroundColor;
                } catch (restoreError) {
                    console.error('❌ 恢复样式也失败了:', restoreError);
                }

                showPageMessage(`点击执行失败: ${clickExecutionError.message}`, 'error');
            }
        }, 500);

        const message = `已点击 ${description}`;
        console.log('📢 显示成功消息:', message);
        showPageMessage(message, 'success');
        return message;

    } catch (error) {
        console.error('❌ 点击操作失败:', {
            错误信息: error.message,
            错误堆栈: error.stack,
            元素信息: {
                标签: element?.tagName || '未知',
                ID: element?.id || '无',
                类名: element?.className || '无'
            }
        });

        const message = `点击失败: ${error.message}`;
        showPageMessage(message, 'error');
        return message;
    }
}

/**
 * 执行焦点操作
 * 使指定元素获得焦点（主要用于输入框）
 */
function performFocus(element, description) {
    try {
        console.log('🎯 执行焦点操作:', description);

        // 滚动到元素可见位置
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 高亮显示要聚焦的元素
        const originalOutline = element.style.outline;
        element.style.outline = '3px solid #4285f4';

        // 延迟执行焦点操作
        setTimeout(() => {
            // 使元素获得焦点
            element.focus();

            // 如果是输入框，选中所有文本
            if (element.select && typeof element.select === 'function') {
                element.select();
            }

            // 恢复原始样式
            element.style.outline = originalOutline;

            console.log('✅ 焦点操作完成:', {
                元素: element.tagName,
                类型: element.type,
                占位符: element.placeholder || '',
                位置: element.getBoundingClientRect()
            });
        }, 500);

        const message = `已聚焦 ${description}`;
        showPageMessage(message, 'success');
        return message;

    } catch (error) {
        console.error('❌ 焦点操作失败:', error);
        const message = `聚焦失败: ${error.message}`;
        showPageMessage(message, 'error');
        return message;
    }
}

/**
 * 执行输入操作
 * 在指定元素中输入文本内容
 */
function performInput(element, content, description) {
    console.log('⌨️ 准备执行输入操作...');
    console.log('📋 输入目标详情:', {
        描述: description,
        输入内容: content,
        内容长度: content?.length || 0,
        元素标签: element.tagName,
        元素ID: element.id || '(无ID)',
        元素类名: element.className || '(无类名)',
        元素类型: element.type || '(无类型)',
        元素占位符: element.placeholder || '(无占位符)',
        元素当前值: element.value || '(空)',
        元素位置: element.getBoundingClientRect()
    });

    try {
        // 检查输入参数
        console.log('🔍 检查输入参数...');
        if (!element) {
            throw new Error('输入元素为null或undefined');
        }

        if (content === null || content === undefined) {
            throw new Error('输入内容为null或undefined');
        }

        if (typeof content !== 'string') {
            console.warn('⚠️ 输入内容不是字符串，尝试转换:', typeof content);
            content = String(content);
        }

        // 检查元素是否可输入
        console.log('🔍 检查元素可输入性...');
        if (!isElementVisible(element)) {
            throw new Error('输入元素不可见');
        }

        if (element.disabled) {
            throw new Error('输入元素被禁用');
        }

        if (element.readOnly) {
            console.warn('⚠️ 输入元素为只读，但仍尝试输入');
        }

        console.log('✅ 输入参数和元素检查通过');

        // 滚动到元素可见位置
        console.log('📜 滚动到输入元素位置...');
        try {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            console.log('✅ 滚动完成');
        } catch (scrollError) {
            console.warn('⚠️ 滚动失败，但继续执行输入:', scrollError);
        }

        // 高亮显示要输入的元素
        console.log('🎨 高亮显示输入元素...');
        const originalOutline = element.style.outline;
        const originalBackgroundColor = element.style.backgroundColor;

        try {
            element.style.outline = '3px solid #4285f4';
            element.style.backgroundColor = '#e3f2fd';
            console.log('✅ 输入元素高亮完成');
        } catch (styleError) {
            console.warn('⚠️ 设置高亮样式失败:', styleError);
        }

        // 延迟执行输入操作，让用户看到高亮效果
        console.log('⏱️ 延迟0.5秒后执行输入...');
        setTimeout(() => {
            try {
                console.log('⌨️ 开始执行输入操作...');

                // 使元素获得焦点
                console.log('🎯 使输入元素获得焦点...');
                try {
                    element.focus();
                    console.log('✅ 输入元素已获得焦点');
                } catch (focusError) {
                    console.warn('⚠️ 获得焦点失败:', focusError);
                }

                // 记录原始值
                const originalValue = element.value;
                console.log('📝 输入元素原始值:', originalValue);

                // 清空输入框（可选，根据需要决定是否清空）
                console.log('🔍 检查是否需要清空输入框...');
                const shouldClear = shouldClearInput(element, content);
                console.log('📋 清空决策结果:', shouldClear);

                if (shouldClear) {
                    try {
                        element.value = '';
                        console.log('🧹 已清空输入框原有内容');

                        // 触发清空后的事件
                        const clearEvent = new Event('input', { bubbles: true });
                        element.dispatchEvent(clearEvent);
                    } catch (clearError) {
                        console.warn('⚠️ 清空输入框失败:', clearError);
                    }
                }

                // 设置输入内容
                console.log('📝 设置输入内容:', content);
                try {
                    element.value = content;
                    console.log('✅ 输入内容设置成功，当前值:', element.value);
                } catch (setValueError) {
                    console.error('❌ 设置输入内容失败:', setValueError);
                    throw setValueError;
                }

                // 触发输入事件，确保网页能够检测到输入变化
                console.log('🔔 触发input事件...');
                try {
                    const inputEvent = new Event('input', {
                        bubbles: true,
                        cancelable: true
                    });
                    element.dispatchEvent(inputEvent);
                    console.log('✅ input事件触发成功');
                } catch (inputEventError) {
                    console.warn('⚠️ input事件触发失败:', inputEventError);
                }

                // 触发change事件
                console.log('🔔 触发change事件...');
                try {
                    const changeEvent = new Event('change', {
                        bubbles: true,
                        cancelable: true
                    });
                    element.dispatchEvent(changeEvent);
                    console.log('✅ change事件触发成功');
                } catch (changeEventError) {
                    console.warn('⚠️ change事件触发失败:', changeEventError);
                }

                // 触发其他可能需要的事件
                console.log('🔔 触发其他输入相关事件...');
                const additionalEvents = ['keyup', 'blur'];
                additionalEvents.forEach(eventType => {
                    try {
                        const event = new Event(eventType, { bubbles: true });
                        element.dispatchEvent(event);
                        console.log(`✅ ${eventType}事件触发成功`);
                    } catch (eventError) {
                        console.warn(`⚠️ ${eventType}事件触发失败:`, eventError);
                    }
                });

                // 如果是密码框，选中所有文本（安全考虑）
                if (element.type === 'password') {
                    console.log('🔒 密码框特殊处理：选中所有文本');
                    try {
                        element.select();
                        console.log('✅ 密码框文本已选中');
                    } catch (selectError) {
                        console.warn('⚠️ 选中密码框文本失败:', selectError);
                    }
                }

                // 恢复原始样式
                console.log('🎨 恢复元素原始样式...');
                try {
                    element.style.outline = originalOutline;
                    element.style.backgroundColor = originalBackgroundColor;
                    console.log('✅ 元素样式已恢复');
                } catch (restoreError) {
                    console.warn('⚠️ 恢复样式失败:', restoreError);
                }

                console.log('✅ 输入操作完成:', {
                    元素: element.tagName,
                    类型: element.type,
                    原始值: originalValue,
                    输入内容: content,
                    最终值: element.value,
                    值是否改变: originalValue !== element.value,
                    位置: element.getBoundingClientRect(),
                    时间: new Date().toLocaleTimeString()
                });

            } catch (inputExecutionError) {
                console.error('❌ 输入执行过程中发生错误:', inputExecutionError);

                // 尝试恢复样式
                try {
                    element.style.outline = originalOutline;
                    element.style.backgroundColor = originalBackgroundColor;
                } catch (restoreError) {
                    console.error('❌ 恢复样式也失败了:', restoreError);
                }

                showPageMessage(`输入执行失败: ${inputExecutionError.message}`, 'error');
            }
        }, 500);

        const message = `已在 ${description} 中输入: "${content}"`;
        console.log('📢 显示成功消息:', message);
        showPageMessage(message, 'success');
        return message;

    } catch (error) {
        console.error('❌ 输入操作失败:', {
            错误信息: error.message,
            错误堆栈: error.stack,
            输入内容: content,
            元素信息: {
                标签: element?.tagName || '未知',
                ID: element?.id || '无',
                类名: element?.className || '无',
                类型: element?.type || '无'
            }
        });

        const message = `输入失败: ${error.message}`;
        showPageMessage(message, 'error');
        return message;
    }
}

/**
 * 判断是否应该清空输入框
 * 根据输入框类型和内容决定是否清空
 */
function shouldClearInput(element, newContent) {
    console.log('🔍 分析是否需要清空输入框...');
    console.log('📋 清空分析参数:', {
        元素类型: element.type || '(无类型)',
        当前值: element.value || '(空)',
        当前值长度: element.value?.length || 0,
        新内容: newContent || '(空)',
        新内容长度: newContent?.length || 0,
        元素ID: element.id || '(无ID)',
        元素占位符: element.placeholder || '(无占位符)'
    });

    try {
        // 如果输入框为空，不需要清空
        if (!element.value || element.value.trim().length === 0) {
            console.log('✅ 输入框为空，无需清空');
            return false;
        }

        // 如果新内容为空，不清空（避免意外清空）
        if (!newContent || newContent.trim().length === 0) {
            console.log('⚠️ 新内容为空，不清空输入框（避免意外清空）');
            return false;
        }

        // 对于密码框，总是清空（安全考虑）
        if (element.type === 'password') {
            console.log('🔒 密码框类型，出于安全考虑总是清空');
            return true;
        }

        // 对于搜索框，通常清空
        if (element.type === 'search') {
            console.log('🔍 搜索框类型，通常清空以输入新的搜索内容');
            return true;
        }

        // 对于其他类型的输入框，如果原有内容很短（可能是默认值），则清空
        if (element.value.length <= 10) {
            console.log('📏 原有内容较短（≤10字符），可能是默认值，选择清空');
            return true;
        }

        // 如果原有内容较长，默认不清空（保留用户数据）
        console.log('📝 输入框已有较长内容，为保护用户数据选择不清空');
        console.log('💡 提示: 如需清空，可先手动清空输入框再执行指令');
        return false;

    } catch (error) {
        console.error('❌ 分析清空策略时发生错误:', error);
        console.log('🛡️ 出于安全考虑，默认不清空');
        return false;
    }
}

/**
 * 在页面上显示消息
 */
function showPageMessage(message, type = 'info') {
    // 移除之前的消息
    const existingMessage = document.getElementById('chrome-extension-message');
    if (existingMessage) {
        existingMessage.remove();
    }

    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.id = 'chrome-extension-message';
    messageDiv.textContent = message;

    // 设置样式
    const colors = {
        success: { bg: '#4caf50', text: '#fff' },
        error: { bg: '#f44336', text: '#fff' },
        info: { bg: '#2196f3', text: '#fff' },
        warning: { bg: '#ff9800', text: '#fff' }
    };

    const color = colors[type] || colors.info;

    Object.assign(messageDiv.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        backgroundColor: color.bg,
        color: color.text,
        padding: '12px 20px',
        borderRadius: '6px',
        fontSize: '14px',
        fontFamily: 'Arial, sans-serif',
        zIndex: '999999',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
        maxWidth: '300px',
        wordWrap: 'break-word'
    });

    // 添加到页面
    document.body.appendChild(messageDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);

    return `消息已显示: ${message}`;
}

/**
 * 高亮指定元素
 */
function highlightElements(selector, color, description) {
    try {
        const elements = document.querySelectorAll(selector);

        elements.forEach(element => {
            element.style.outline = `3px solid ${color}`;
            element.style.outlineOffset = '2px';
            element.setAttribute('data-extension-highlighted', 'true');
        });

        return `已高亮 ${elements.length} 个${description}元素`;

    } catch (error) {
        return `高亮失败: ${error.message}`;
    }
}

/**
 * 清除所有高亮
 */
function clearHighlights() {
    try {
        const highlightedElements = document.querySelectorAll('[data-extension-highlighted="true"]');

        highlightedElements.forEach(element => {
            element.style.outline = '';
            element.style.outlineOffset = '';
            element.removeAttribute('data-extension-highlighted');
        });

        return `已清除 ${highlightedElements.length} 个高亮元素`;

    } catch (error) {
        return `清除高亮失败: ${error.message}`;
    }
}

/**
 * 点击元素
 */
function clickElement(selector) {
    try {
        const element = document.querySelector(selector);

        if (!element) {
            return `未找到选择器 "${selector}" 对应的元素`;
        }

        element.click();
        return `已点击元素: ${selector}`;

    } catch (error) {
        return `点击失败: ${error.message}`;
    }
}

/**
 * 隐藏元素
 */
function hideElements(selector) {
    try {
        const elements = document.querySelectorAll(selector);

        elements.forEach(element => {
            element.style.display = 'none';
            element.setAttribute('data-extension-hidden', 'true');
        });

        return `已隐藏 ${elements.length} 个元素`;

    } catch (error) {
        return `隐藏失败: ${error.message}`;
    }
}

/**
 * 显示元素
 */
function showElements(selector) {
    try {
        const elements = document.querySelectorAll(selector);

        elements.forEach(element => {
            if (element.getAttribute('data-extension-hidden') === 'true') {
                element.style.display = '';
                element.removeAttribute('data-extension-hidden');
            }
        });

        return `已显示 ${elements.length} 个元素`;

    } catch (error) {
        return `显示失败: ${error.message}`;
    }
}

/**
 * 显示页面信息
 */
function showPageInfo() {
    const info = {
        url: window.location.href,
        title: document.title,
        domain: window.location.hostname,
        links: document.querySelectorAll('a').length,
        images: document.querySelectorAll('img').length,
        forms: document.querySelectorAll('form').length
    };

    const infoText = `页面信息:\n标题: ${info.title}\n域名: ${info.domain}\n链接: ${info.links}个\n图片: ${info.images}个\n表单: ${info.forms}个`;

    showPageMessage(infoText, 'info');
    return infoText;
}

/**
 * 清除页面效果
 */
function clearPageEffects() {
    // 清除高亮
    clearHighlights();

    // 移除消息
    const message = document.getElementById('chrome-extension-message');
    if (message) {
        message.remove();
    }

    // 显示隐藏的元素
    const hiddenElements = document.querySelectorAll('[data-extension-hidden="true"]');
    hiddenElements.forEach(element => {
        element.style.display = '';
        element.removeAttribute('data-extension-hidden');
    });
}

/**
 * 注入页面标识
 */
function injectPageIdentifier() {
    // 在页面上添加一个隐藏的标识，表明插件已加载
    if (!document.getElementById('chrome-extension-identifier')) {
        const identifier = document.createElement('div');
        identifier.id = 'chrome-extension-identifier';
        identifier.style.display = 'none';
        identifier.setAttribute('data-extension-loaded', 'true');
        identifier.setAttribute('data-load-time', Date.now().toString());
        document.body.appendChild(identifier);
    }
}

/**
 * 创建调试面板
 */
function createDebugPanel() {
    if (document.getElementById('chrome-extension-debug-panel')) {
        return; // 已存在
    }

    const panel = document.createElement('div');
    panel.id = 'chrome-extension-debug-panel';
    panel.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 10px;">🔧 插件调试面板</div>
        <div>页面: ${document.title}</div>
        <div>URL: ${window.location.href}</div>
        <div>加载时间: ${new Date().toLocaleTimeString()}</div>
        <div>指令历史: ${commandHistory.length} 条</div>
    `;

    Object.assign(panel.style, {
        position: 'fixed',
        bottom: '20px',
        left: '20px',
        backgroundColor: '#333',
        color: '#fff',
        padding: '15px',
        borderRadius: '8px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: '999998',
        maxWidth: '300px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.5)'
    });

    document.body.appendChild(panel);
}

/**
 * 移除调试面板
 */
function removeDebugPanel() {
    const panel = document.getElementById('chrome-extension-debug-panel');
    if (panel) {
        panel.remove();
    }
}

/**
 * 初始化和兼容性处理
 * 确保content script在DOM加载完成后立即开始监听
 */

// 添加初始化日志
console.log('📦 Content script文件加载完成');
console.log('🚀 准备初始化content script...');

// 页面加载完成后立即初始化
if (document.readyState === 'loading') {
    console.log('⏳ DOM正在加载中，等待DOMContentLoaded事件...');
    document.addEventListener('DOMContentLoaded', () => {
        console.log('📄 DOM加载完成，开始初始化');
        initializeContentScript();
    });
} else {
    console.log('📄 DOM已加载完成，立即初始化');
    initializeContentScript();
}

// 添加页面卸载时的清理
window.addEventListener('beforeunload', () => {
    console.log('🔄 页面即将卸载，清理content script状态');
    clearPageEffects();
});

// 导出增强的调试接口
window.extensionDebug = {
    // 基础功能
    getHistory: () => commandHistory,
    getStatus: () => ({
        isInitialized,
        lastExecutedCommand,
        debugMode,
        historyCount: commandHistory.length,
        pageUrl: window.location.href,
        pageTitle: document.title
    }),
    executeCommand: executeCommand,
    clearEffects: clearPageEffects,

    // 新增调试功能
    getLastCommands: (count = 5) => commandHistory.slice(-count),
    clearHistory: () => {
        commandHistory = [];
        console.log('🗑️ 指令历史已清空');
    },
    testCommand: (command) => {
        console.log(`🧪 测试执行指令: "${command}"`);
        return executeCommand(command, false, 'debug');
    },

    // 状态查询
    isReady: () => isInitialized,
    getDebugInfo: () => ({
        initialized: isInitialized,
        debugMode: debugMode,
        commandCount: commandHistory.length,
        lastCommand: lastExecutedCommand,
        pageInfo: {
            url: window.location.href,
            title: document.title,
            readyState: document.readyState
        }
    })
};

/**
 * 处理"标签页"类指令
 * 智能识别和处理各种标签页操作指令
 */
function handleTabCommand(originalCommand, trimmedCommand) {
    console.log('🔄 开始处理标签页指令:', originalCommand);

    try {
        // 解析标签页操作类型和参数
        const tabInfo = parseTabCommand(originalCommand, trimmedCommand);

        if (!tabInfo) {
            const message = '无法解析标签页指令';
            console.warn('⚠️', message);
            return showPageMessage(message, 'warning');
        }

        console.log('🔄 标签页指令解析结果:', tabInfo);

        // 发送消息到background script执行标签页操作
        chrome.runtime.sendMessage({
            action: 'tab_operation',
            data: {
                operation: tabInfo.operation,
                params: tabInfo.params
            }
        }, (response) => {
            if (response && response.success) {
                const message = response.message || `标签页操作 ${tabInfo.operation} 执行成功`;
                console.log('✅ 标签页操作成功:', message);
                showPageMessage(message, 'success');
            } else {
                const errorMessage = response?.error || '标签页操作失败';
                console.error('❌ 标签页操作失败:', errorMessage);
                showPageMessage(`标签页操作失败: ${errorMessage}`, 'error');
            }
        });

        return `正在执行标签页操作: ${tabInfo.operation}`;

    } catch (error) {
        console.error('❌ 标签页指令处理失败:', error);
        return showPageMessage(`标签页指令执行失败: ${error.message}`, 'error');
    }
}

/**
 * 解析标签页指令
 * 支持多种标签页指令格式
 */
function parseTabCommand(originalCommand, trimmedCommand) {
    console.log('🔍 解析标签页指令格式:', originalCommand);

    let operation = '';
    let params = {};

    // 分析指令确定操作类型
    const lowerCommand = trimmedCommand.toLowerCase();

    if (lowerCommand.includes('切换') || lowerCommand.includes('打开') || lowerCommand.includes('跳转') || lowerCommand.includes('转到')) {
        operation = 'switch_to_tab';

        // 提取目标信息
        const patterns = [
            /(?:切换到|打开|跳转到|转到)(?:第)?(\d+)(?:个)?(?:标签页|选项卡|tab)/,
            /(?:切换到|打开|跳转到|转到)(.+?)(?:标签页|选项卡|tab)/,
            /(?:切换到|打开|跳转到|转到)(.+)/
        ];

        for (const pattern of patterns) {
            const match = trimmedCommand.match(pattern);
            if (match) {
                const target = match[1].trim();
                const numberMatch = target.match(/(\d+)/);

                if (numberMatch) {
                    params.index = parseInt(numberMatch[1]);
                    console.log(`📋 解析到标签页索引: ${params.index}`);
                } else if (target.includes('http') || target.includes('www') || target.includes('.com')) {
                    params.url = target;
                    console.log(`📋 解析到URL参数: ${params.url}`);
                } else {
                    params.title = target;
                    console.log(`📋 解析到标题参数: ${params.title}`);
                }
                break;
            }
        }

    } else if (lowerCommand.includes('关闭') || lowerCommand.includes('删除')) {
        operation = 'close_tab';

        // 提取要关闭的标签页信息
        const patterns = [
            /(?:关闭|删除)(?:第)?(\d+)(?:个)?(?:标签页|选项卡|tab)/,
            /(?:关闭|删除)(?:当前)?(?:标签页|选项卡|tab)/,
            /(?:关闭|删除)(.+?)(?:标签页|选项卡|tab)/
        ];

        for (const pattern of patterns) {
            const match = trimmedCommand.match(pattern);
            if (match) {
                if (match[1]) {
                    const target = match[1].trim();
                    const numberMatch = target.match(/(\d+)/);

                    if (numberMatch) {
                        params.index = parseInt(numberMatch[1]);
                    } else if (target.includes('当前') || target === '') {
                        params.current = true;
                    } else {
                        params.title = target;
                    }
                } else {
                    params.current = true;
                }
                break;
            }
        }

    } else if (lowerCommand.includes('新建') || lowerCommand.includes('创建') || lowerCommand.includes('打开新')) {
        operation = 'create_tab';

        // 提取新标签页的URL
        const patterns = [
            /(?:新建|创建|打开新)(?:标签页|选项卡|tab)(?:到|去)?(.+)/,
            /(?:新建|创建|打开新)(.+?)(?:标签页|选项卡|tab)/
        ];

        for (const pattern of patterns) {
            const match = trimmedCommand.match(pattern);
            if (match && match[1]) {
                const target = match[1].trim();
                if (target && target !== '标签页' && target !== '选项卡' && target !== 'tab') {
                    if (target.includes('http') || target.includes('www') || target.includes('.com')) {
                        params.url = target;
                    } else {
                        // 构建搜索URL
                        params.url = `https://www.google.com/search?q=${encodeURIComponent(target)}`;
                    }
                }
                break;
            }
        }

    } else if (lowerCommand.includes('刷新') || lowerCommand.includes('重新加载') || lowerCommand.includes('重载')) {
        operation = 'reload_tab';

        // 检查是否指定了特定标签页
        const patterns = [
            /(?:刷新|重新加载|重载)(?:第)?(\d+)(?:个)?(?:标签页|选项卡|tab)/,
            /(?:刷新|重新加载|重载)(?:当前)?(?:标签页|选项卡|tab)/
        ];

        for (const pattern of patterns) {
            const match = trimmedCommand.match(pattern);
            if (match) {
                if (match[1]) {
                    const numberMatch = match[1].match(/(\d+)/);
                    if (numberMatch) {
                        params.index = parseInt(numberMatch[1]);
                    }
                } else {
                    params.current = true;
                }
                break;
            }
        }

        if (Object.keys(params).length === 0) {
            params.current = true; // 默认刷新当前标签页
        }

    } else if (lowerCommand.includes('复制') || lowerCommand.includes('克隆')) {
        operation = 'duplicate_tab';

        // 检查是否指定了特定标签页
        const patterns = [
            /(?:复制|克隆)(?:第)?(\d+)(?:个)?(?:标签页|选项卡|tab)/,
            /(?:复制|克隆)(?:当前)?(?:标签页|选项卡|tab)/
        ];

        for (const pattern of patterns) {
            const match = trimmedCommand.match(pattern);
            if (match) {
                if (match[1]) {
                    const numberMatch = match[1].match(/(\d+)/);
                    if (numberMatch) {
                        params.index = parseInt(numberMatch[1]);
                    }
                } else {
                    params.current = true;
                }
                break;
            }
        }

        if (Object.keys(params).length === 0) {
            params.current = true; // 默认复制当前标签页
        }
    }

    if (!operation) {
        console.log('❌ 无法识别标签页操作类型');
        return null;
    }

    console.log('🔄 标签页指令解析结果:', { operation, params });

    return {
        operation: operation,
        params: params
    };
}

console.log('✅ Content script已加载，开始监听popup指令');
console.log('🎯 调试接口已注册到 window.extensionDebug');
console.log('📋 可用的调试命令:');
console.log('   - extensionDebug.getStatus() - 获取状态信息');
console.log('   - extensionDebug.getHistory() - 获取指令历史');
console.log('   - extensionDebug.testCommand("hello") - 测试指令执行');
console.log('   - extensionDebug.getDebugInfo() - 获取详细调试信息');
