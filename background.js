/**
 * Chrome扩展后台脚本
 * 处理标签页操作和其他需要特殊权限的功能
 */

console.log('🔧 Background script 已加载');

// 监听来自popup和content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('📨 Background收到消息:', message);

    try {
        const { action, data } = message;

        switch (action) {
            case 'tab_operation':
                handleTabOperation(data, sendResponse);
                return true; // 保持消息通道开放以进行异步响应

            case 'get_tabs_info':
                getTabsInfo(sendResponse);
                return true;

            case 'ping_background':
                sendResponse({ success: true, message: 'Background script is running' });
                break;

            default:
                console.warn('⚠️ 未知的background动作:', action);
                sendResponse({ success: false, error: `未知的动作: ${action}` });
        }
    } catch (error) {
        console.error('❌ Background处理消息时出错:', error);
        sendResponse({ success: false, error: error.message });
    }
});

/**
 * 处理标签页操作
 */
async function handleTabOperation(data, sendResponse) {
    console.log('🔄 处理标签页操作:', data);

    try {
        const { operation, params } = data;

        switch (operation) {
            case 'switch_to_tab':
                await switchToTab(params);
                break;

            case 'close_tab':
                await closeTab(params);
                break;

            case 'create_tab':
                await createTab(params);
                break;

            case 'reload_tab':
                await reloadTab(params);
                break;

            case 'duplicate_tab':
                await duplicateTab(params);
                break;

            case 'move_tab':
                await moveTab(params);
                break;

            default:
                throw new Error(`未知的标签页操作: ${operation}`);
        }

        sendResponse({ success: true, message: `标签页操作 ${operation} 执行成功` });

    } catch (error) {
        console.error('❌ 标签页操作失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}

/**
 * 切换到指定标签页
 */
async function switchToTab(params) {
    console.log('🔄 切换标签页:', params);

    const { index, url, title } = params;

    // 获取当前窗口的所有标签页
    const tabs = await chrome.tabs.query({ currentWindow: true });
    console.log('📋 当前窗口标签页列表:', tabs.map(tab => ({
        index: tab.index,
        title: tab.title,
        url: tab.url,
        active: tab.active
    })));

    let targetTab = null;

    if (typeof index === 'number') {
        // 按索引查找（从1开始计数，转换为从0开始）
        const tabIndex = index - 1;
        if (tabIndex >= 0 && tabIndex < tabs.length) {
            targetTab = tabs[tabIndex];
            console.log(`✅ 找到第${index}个标签页:`, targetTab.title);
        } else {
            throw new Error(`标签页索引 ${index} 超出范围 (1-${tabs.length})`);
        }
    } else if (url) {
        // 按URL查找
        targetTab = tabs.find(tab => tab.url.includes(url));
        if (targetTab) {
            console.log(`✅ 找到URL包含"${url}"的标签页:`, targetTab.title);
        } else {
            throw new Error(`未找到URL包含"${url}"的标签页`);
        }
    } else if (title) {
        // 按标题查找
        targetTab = tabs.find(tab => tab.title.toLowerCase().includes(title.toLowerCase()));
        if (targetTab) {
            console.log(`✅ 找到标题包含"${title}"的标签页:`, targetTab.title);
        } else {
            throw new Error(`未找到标题包含"${title}"的标签页`);
        }
    } else {
        throw new Error('必须提供 index、url 或 title 参数');
    }

    if (targetTab) {
        // 激活目标标签页
        await chrome.tabs.update(targetTab.id, { active: true });
        console.log('✅ 标签页切换成功:', {
            id: targetTab.id,
            index: targetTab.index + 1, // 显示为从1开始的索引
            title: targetTab.title,
            url: targetTab.url
        });
    }
}

/**
 * 关闭标签页
 */
async function closeTab(params) {
    console.log('❌ 关闭标签页:', params);

    const { index, url, title, current } = params;

    if (current) {
        // 关闭当前标签页
        const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
        await chrome.tabs.remove(currentTab.id);
        console.log('✅ 当前标签页已关闭');
        return;
    }

    // 获取所有标签页
    const tabs = await chrome.tabs.query({ currentWindow: true });
    let targetTab = null;

    if (typeof index === 'number') {
        const tabIndex = index - 1;
        if (tabIndex >= 0 && tabIndex < tabs.length) {
            targetTab = tabs[tabIndex];
        }
    } else if (url) {
        targetTab = tabs.find(tab => tab.url.includes(url));
    } else if (title) {
        targetTab = tabs.find(tab => tab.title.toLowerCase().includes(title.toLowerCase()));
    }

    if (targetTab) {
        await chrome.tabs.remove(targetTab.id);
        console.log('✅ 标签页已关闭:', targetTab.title);
    } else {
        throw new Error('未找到要关闭的标签页');
    }
}

/**
 * 创建新标签页
 */
async function createTab(params) {
    console.log('➕ 创建新标签页:', params);

    const { url, active = true } = params;

    const newTab = await chrome.tabs.create({
        url: url || 'chrome://newtab/',
        active: active
    });

    console.log('✅ 新标签页已创建:', {
        id: newTab.id,
        url: newTab.url,
        active: newTab.active
    });
}

/**
 * 重新加载标签页
 */
async function reloadTab(params) {
    console.log('🔄 重新加载标签页:', params);

    const { index, current = false } = params;

    if (current) {
        // 重新加载当前标签页
        const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
        await chrome.tabs.reload(currentTab.id);
        console.log('✅ 当前标签页已重新加载');
        return;
    }

    if (typeof index === 'number') {
        const tabs = await chrome.tabs.query({ currentWindow: true });
        const tabIndex = index - 1;
        if (tabIndex >= 0 && tabIndex < tabs.length) {
            const targetTab = tabs[tabIndex];
            await chrome.tabs.reload(targetTab.id);
            console.log(`✅ 第${index}个标签页已重新加载:`, targetTab.title);
        } else {
            throw new Error(`标签页索引 ${index} 超出范围`);
        }
    } else {
        throw new Error('必须提供 index 参数或设置 current 为 true');
    }
}

/**
 * 复制标签页
 */
async function duplicateTab(params) {
    console.log('📋 复制标签页:', params);

    const { index, current = false } = params;

    let targetTab;

    if (current) {
        [targetTab] = await chrome.tabs.query({ active: true, currentWindow: true });
    } else if (typeof index === 'number') {
        const tabs = await chrome.tabs.query({ currentWindow: true });
        const tabIndex = index - 1;
        if (tabIndex >= 0 && tabIndex < tabs.length) {
            targetTab = tabs[tabIndex];
        } else {
            throw new Error(`标签页索引 ${index} 超出范围`);
        }
    } else {
        throw new Error('必须提供 index 参数或设置 current 为 true');
    }

    if (targetTab) {
        const duplicatedTab = await chrome.tabs.duplicate(targetTab.id);
        console.log('✅ 标签页已复制:', {
            原标签页: targetTab.title,
            新标签页ID: duplicatedTab.id
        });
    }
}

/**
 * 移动标签页
 */
async function moveTab(params) {
    console.log('🔄 移动标签页:', params);

    const { fromIndex, toIndex } = params;

    const tabs = await chrome.tabs.query({ currentWindow: true });

    const fromTabIndex = fromIndex - 1;
    const toTabIndex = toIndex - 1;

    if (fromTabIndex < 0 || fromTabIndex >= tabs.length) {
        throw new Error(`源标签页索引 ${fromIndex} 超出范围`);
    }

    if (toTabIndex < 0 || toTabIndex >= tabs.length) {
        throw new Error(`目标标签页索引 ${toIndex} 超出范围`);
    }

    const targetTab = tabs[fromTabIndex];
    await chrome.tabs.move(targetTab.id, { index: toTabIndex });

    console.log(`✅ 标签页已移动: 从第${fromIndex}个位置移动到第${toIndex}个位置`);
}

/**
 * 获取标签页信息
 */
async function getTabsInfo(sendResponse) {
    console.log('📋 获取标签页信息');

    try {
        const tabs = await chrome.tabs.query({ currentWindow: true });
        const tabsInfo = tabs.map((tab, index) => ({
            index: index + 1, // 从1开始计数
            id: tab.id,
            title: tab.title,
            url: tab.url,
            active: tab.active,
            pinned: tab.pinned,
            favIconUrl: tab.favIconUrl
        }));

        console.log('📋 标签页信息:', tabsInfo);

        sendResponse({
            success: true,
            tabs: tabsInfo,
            totalCount: tabs.length
        });

    } catch (error) {
        console.error('❌ 获取标签页信息失败:', error);
        sendResponse({ success: false, error: error.message });
    }
}
