# 🎤 智能语音快捷指令功能使用指南

## 📋 功能概述

智能语音快捷指令功能允许用户通过自然语言快速导航到常用网站，支持个人化定制和智能匹配。

## ✨ 主要特性

### 1. 自然语言网站导航
- 支持日常用语，如"打开我的邮箱"、"进入我的购物车"
- 自动映射到常用网站（Gmail、淘宝等）
- 模糊匹配，即使表述不完全准确也能识别意图

### 2. 个人化语音快捷方式
- 创建和管理自定义语音命令
- 将短语绑定到特定URL
- 支持同一网站的多个触发词

### 3. 快捷指令管理系统
- 直观的界面查看、编辑和删除快捷指令
- 导入/导出功能
- 使用频率统计

## 🚀 使用方法

### 基本使用

1. **打开插件**：点击浏览器工具栏中的插件图标
2. **语音输入**：
   - 点击麦克风图标开始语音识别
   - 或使用 Win+H 系统语音输入
   - 说出快捷指令，如"打开我的邮箱"
3. **自动执行**：插件会自动识别并打开对应网站

### 管理快捷指令

1. **打开管理界面**：点击"⚡ 快捷指令"按钮
2. **添加新指令**：
   - 在"语音指令"框输入触发词
   - 在"目标URL"框输入网址
   - 点击"添加"按钮
3. **编辑指令**：点击指令列表中的"编辑"按钮
4. **删除指令**：点击指令列表中的"删除"按钮

## 📝 默认快捷指令

插件预置了以下常用快捷指令：

### 邮箱相关
- "打开我的邮箱" → Gmail
- "进入邮箱" → Gmail
- "查看邮件" → Gmail
- "打开Gmail" → Gmail

### 购物相关
- "进入我的购物车" → 淘宝购物车
- "打开购物车" → 淘宝购物车
- "去淘宝" → 淘宝首页
- "打开京东" → 京东首页

### 社交媒体
- "打开微博" → 微博
- "进入微信网页版" → 微信网页版
- "打开知乎" → 知乎
- "去B站" → 哔哩哔哩

### 工作相关
- "我的工作台" → 可自定义
- "打开GitHub" → GitHub
- "进入云文档" → 腾讯文档

### 搜索引擎
- "百度搜索" → 百度
- "谷歌搜索" → 谷歌

## 🔧 高级功能

### 导入/导出配置

1. **导出配置**：
   - 点击"📤 导出配置"按钮
   - 自动下载JSON配置文件
   - 文件包含所有快捷指令和使用统计

2. **导入配置**：
   - 点击"📥 导入配置"按钮
   - 选择之前导出的JSON文件
   - 确认导入后覆盖现有配置

### 使用统计

- 插件会自动记录每个快捷指令的使用次数
- 在管理界面中按使用频率排序显示
- 帮助优化常用指令的配置

## 🎯 智能匹配机制

插件使用三级匹配机制：

1. **精确匹配**：完全匹配指令名称
2. **包含匹配**：指令包含输入内容或输入包含指令
3. **模糊匹配**：基于关键词的智能匹配

## 💡 使用技巧

### 语音输入优化
- 说话清晰，语速适中
- 使用简短明确的指令
- 避免背景噪音干扰

### 指令设计建议
- 使用自然的日常用语
- 避免过于复杂的表述
- 为同一网站设置多个触发词

### 个人化定制
- 根据个人习惯设置指令
- 定期清理不常用的指令
- 利用使用统计优化配置

## 🔍 故障排除

### 语音识别问题
- 检查麦克风权限
- 确保网络连接正常
- 尝试使用 Win+H 系统语音输入

### 快捷指令不生效
- 检查URL格式是否正确
- 确认指令名称没有冲突
- 查看控制台日志获取详细信息

### 存储问题
- 检查浏览器存储权限
- 尝试重新安装插件
- 清除浏览器缓存后重试

## 📞 技术支持

如遇到问题，请：
1. 查看浏览器控制台日志
2. 检查插件权限设置
3. 尝试重新加载插件
4. 联系技术支持团队

---

**注意**：此功能需要浏览器支持语音识别API，建议使用最新版本的Chrome浏览器以获得最佳体验。
