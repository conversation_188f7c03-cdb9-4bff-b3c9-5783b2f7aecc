/* 弹出界面样式 */
body {
  width: 350px;
  min-height: 200px;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

.container {
  padding: 20px;
  background: white;
  margin: 0;
  border-radius: 0;
}

.header {
  text-align: center;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  color: #4285f4;
  font-weight: 600;
}

.header p {
  margin: 5px 0 0 0;
  font-size: 12px;
  color: #666;
}

.input-section {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.command-input {
  width: 100%;
  padding: 12px 45px 12px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  box-sizing: border-box;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
}

.command-input:focus {
  border-color: #4285f4;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

.command-input::placeholder {
  color: #999;
}

.voice-indicator {
  position: absolute;
  right: 12px;
  font-size: 16px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.3s ease, transform 0.2s ease;
  -webkit-user-select: none;
  user-select: none;
}

.voice-indicator:hover {
  opacity: 1;
  transform: scale(1.1);
}

.voice-indicator.active {
  opacity: 1;
  color: #4285f4;
  animation: pulse 1.5s infinite;
}

.input-hint {
  margin-top: 6px;
  font-size: 11px;
  color: #666;
  text-align: center;
}

.button-section {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.send-button {
  flex: 1;
  padding: 10px 16px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
}

.send-button:hover {
  background: #3367d6;
}

.send-button:active {
  transform: translateY(1px);
}

.clear-button {
  flex: 1;
  padding: 10px 16px;
  background: #f8f9fa;
  color: #5f6368;
  border: 1px solid #dadce0;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
}

.clear-button:hover {
  background: #f1f3f4;
}

.clear-button:active {
  transform: translateY(1px);
}

.shortcut-manager-button {
  flex: 1;
  padding: 10px 16px;
  background: #34a853;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
}

.shortcut-manager-button:hover {
  background: #2d8f47;
}

.shortcut-manager-button:active {
  transform: translateY(1px);
}

.status-section {
  margin-top: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #4285f4;
}

.status-text {
  margin: 0;
  font-size: 12px;
  color: #5f6368;
}

.status-text.success {
  color: #137333;
  border-left-color: #137333;
}

.status-text.error {
  color: #d93025;
  border-left-color: #d93025;
}

.feature-list {
  margin-top: 15px;
  padding: 0;
  list-style: none;
}

.feature-list li {
  padding: 4px 0;
  font-size: 11px;
  color: #666;
  position: relative;
  padding-left: 16px;
}

.feature-list li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #4285f4;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 400px) {
  body {
    width: 300px;
  }

  .container {
    padding: 15px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

.container {
  animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* ========================================
   智能语音快捷指令管理界面样式
   ======================================== */

.shortcut-manager {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 1000;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
}

.shortcut-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e1e5e9;
}

.shortcut-header h3 {
  margin: 0;
  font-size: 16px;
  color: #4285f4;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.close-button:hover {
  background: #f1f3f4;
  color: #333;
}

.add-shortcut-section {
  margin-bottom: 25px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.add-shortcut-section h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.shortcut-input {
  padding: 10px 12px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  font-size: 13px;
  box-sizing: border-box;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
}

.shortcut-input:focus {
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
}

.shortcut-input::placeholder {
  color: #999;
}

.add-button {
  padding: 10px 16px;
  background: #34a853;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.add-button:hover {
  background: #2d8f47;
}

.shortcut-list-section {
  margin-bottom: 25px;
}

.shortcut-list-section h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.shortcut-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: white;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.3s ease;
}

.shortcut-item:last-child {
  border-bottom: none;
}

.shortcut-item:hover {
  background: #f8f9fa;
}

.shortcut-info {
  flex: 1;
  min-width: 0;
}

.shortcut-command {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.shortcut-url {
  font-size: 12px;
  color: #4285f4;
  margin-bottom: 2px;
  word-break: break-all;
}

.shortcut-stats {
  font-size: 11px;
  color: #666;
}

.shortcut-actions {
  display: flex;
  gap: 8px;
  margin-left: 10px;
}

.edit-shortcut-btn,
.delete-shortcut-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.edit-shortcut-btn {
  background: #4285f4;
  color: white;
}

.edit-shortcut-btn:hover {
  background: #3367d6;
}

.delete-shortcut-btn {
  background: #ea4335;
  color: white;
}

.delete-shortcut-btn:hover {
  background: #d33b2c;
}

.no-shortcuts {
  text-align: center;
  padding: 30px 20px;
  color: #666;
  font-size: 13px;
  font-style: italic;
}

.import-export-section {
  margin-bottom: 20px;
}

.import-export-section h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.import-export-buttons {
  display: flex;
  gap: 10px;
}

.export-button,
.import-button {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #dadce0;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, border-color 0.3s ease;
  background: white;
  color: #333;
}

.export-button:hover,
.import-button:hover {
  background: #f8f9fa;
  border-color: #4285f4;
}

/* 隐藏元素 */
.hidden {
  display: none !important;
}

.hidden-file-input {
  display: none !important;
}
