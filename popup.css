/* 弹出界面样式 */
body {
  width: 350px;
  min-height: 200px;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

.container {
  padding: 20px;
  background: white;
  margin: 0;
  border-radius: 0;
}

.header {
  text-align: center;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0;
  font-size: 18px;
  color: #4285f4;
  font-weight: 600;
}

.header p {
  margin: 5px 0 0 0;
  font-size: 12px;
  color: #666;
}

.input-section {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.command-input {
  width: 100%;
  padding: 12px 45px 12px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  box-sizing: border-box;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
}

.command-input:focus {
  border-color: #4285f4;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

.command-input::placeholder {
  color: #999;
}

.voice-indicator {
  position: absolute;
  right: 12px;
  font-size: 16px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.3s ease, transform 0.2s ease;
  -webkit-user-select: none;
  user-select: none;
}

.voice-indicator:hover {
  opacity: 1;
  transform: scale(1.1);
}

.voice-indicator.active {
  opacity: 1;
  color: #4285f4;
  animation: pulse 1.5s infinite;
}

.input-hint {
  margin-top: 6px;
  font-size: 11px;
  color: #666;
  text-align: center;
}

.button-section {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.send-button {
  flex: 1;
  padding: 10px 16px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
}

.send-button:hover {
  background: #3367d6;
}

.send-button:active {
  transform: translateY(1px);
}

.clear-button {
  flex: 1;
  padding: 10px 16px;
  background: #f8f9fa;
  color: #5f6368;
  border: 1px solid #dadce0;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
}

.clear-button:hover {
  background: #f1f3f4;
}

.clear-button:active {
  transform: translateY(1px);
}

.status-section {
  margin-top: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #4285f4;
}

.status-text {
  margin: 0;
  font-size: 12px;
  color: #5f6368;
}

.status-text.success {
  color: #137333;
  border-left-color: #137333;
}

.status-text.error {
  color: #d93025;
  border-left-color: #d93025;
}

.feature-list {
  margin-top: 15px;
  padding: 0;
  list-style: none;
}

.feature-list li {
  padding: 4px 0;
  font-size: 11px;
  color: #666;
  position: relative;
  padding-left: 16px;
}

.feature-list li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #4285f4;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 400px) {
  body {
    width: 300px;
  }

  .container {
    padding: 15px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

.container {
  animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
