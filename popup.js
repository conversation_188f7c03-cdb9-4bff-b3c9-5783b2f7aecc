/**
 * 实时指令插件 - Popup界面脚本
 * 负责处理用户输入、语音输入和与content script的通信
 * 支持Win+H语音输入和浏览器内置语音识别功能
 */

// DOM元素引用
let commandInput;
let sendButton;
let clearButton;
let statusText;
let voiceIndicator;

// 状态管理
let isConnected = false;
let lastCommand = '';
let sendTimeout = null;

// 语音输入相关状态
let isVoiceInputActive = false;
let speechRecognition = null;
let voiceInputTimeout = null;
let lastInputLength = 0;

/**
 * 初始化函数
 */
function initialize() {
    console.log('🚀 Popup界面初始化开始...');

    // 获取DOM元素
    commandInput = document.getElementById('commandInput');
    sendButton = document.getElementById('sendButton');
    clearButton = document.getElementById('clearButton');
    statusText = document.getElementById('statusText');
    voiceIndicator = document.getElementById('voiceIndicator');

    // 验证DOM元素
    if (!commandInput || !sendButton || !clearButton || !statusText) {
        console.error('❌ 无法找到必要的DOM元素');
        updateStatus('初始化失败：DOM元素缺失', 'error');
        return;
    }

    // 初始化语音识别功能
    initializeSpeechRecognition();

    // 绑定事件监听器
    setupEventListeners();

    // 检查连接状态
    checkConnection();

    // 聚焦到输入框
    commandInput.focus();

    console.log('✅ Popup界面初始化完成');
    console.log('🎤 语音输入支持状态:', speechRecognition ? '已启用' : '不支持');
}

/**
 * 初始化语音识别功能
 * 支持浏览器内置的Web Speech API
 */
function initializeSpeechRecognition() {
    try {
        // 检查浏览器是否支持语音识别
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        if (!SpeechRecognition) {
            console.warn('⚠️ 浏览器不支持Web Speech API语音识别');
            if (voiceIndicator) {
                voiceIndicator.style.opacity = '0.3';
                voiceIndicator.title = '浏览器不支持语音识别，请使用Win+H系统语音输入';
            }
            return;
        }

        // 创建语音识别实例
        speechRecognition = new SpeechRecognition();

        // 配置语音识别参数
        speechRecognition.continuous = false;          // 不连续识别
        speechRecognition.interimResults = true;       // 显示临时结果
        speechRecognition.lang = 'zh-CN';              // 中文识别
        speechRecognition.maxAlternatives = 1;         // 最多1个候选结果

        // 语音识别开始事件
        speechRecognition.onstart = () => {
            console.log('🎤 语音识别开始');
            isVoiceInputActive = true;
            updateVoiceIndicator(true);
            updateStatus('正在监听语音输入...', 'success');
        };

        // 语音识别结果事件
        speechRecognition.onresult = (event) => {
            let transcript = '';

            // 获取识别结果
            for (let i = event.resultIndex; i < event.results.length; i++) {
                if (event.results[i].isFinal) {
                    transcript += event.results[i][0].transcript;
                } else {
                    // 临时结果，实时显示
                    const interimTranscript = event.results[i][0].transcript;
                    commandInput.value = lastCommand + interimTranscript;
                }
            }

            // 如果有最终结果，更新输入框
            if (transcript) {
                commandInput.value = transcript.trim();
                console.log('🎤 语音识别结果:', transcript);

                // 触发input事件以启动实时传递
                const inputEvent = new Event('input', { bubbles: true });
                commandInput.dispatchEvent(inputEvent);
            }
        };

        // 语音识别结束事件
        speechRecognition.onend = () => {
            console.log('🎤 语音识别结束');
            isVoiceInputActive = false;
            updateVoiceIndicator(false);
            updateStatus('语音输入完成', 'success');
        };

        // 语音识别错误事件
        speechRecognition.onerror = (event) => {
            console.error('❌ 语音识别错误:', event.error);
            isVoiceInputActive = false;
            updateVoiceIndicator(false);

            let errorMessage = '语音识别失败';
            switch (event.error) {
                case 'no-speech':
                    errorMessage = '未检测到语音输入';
                    break;
                case 'audio-capture':
                    errorMessage = '无法访问麦克风';
                    break;
                case 'not-allowed':
                    errorMessage = '麦克风权限被拒绝';
                    break;
                case 'network':
                    errorMessage = '网络连接错误';
                    break;
                default:
                    errorMessage = `语音识别错误: ${event.error}`;
            }

            updateStatus(errorMessage, 'error');
        };

        console.log('✅ 语音识别功能初始化成功');

    } catch (error) {
        console.error('❌ 语音识别初始化失败:', error);
        if (voiceIndicator) {
            voiceIndicator.style.opacity = '0.3';
            voiceIndicator.title = '语音识别初始化失败，请使用Win+H系统语音输入';
        }
    }
}

/**
 * 更新语音指示器状态
 */
function updateVoiceIndicator(isActive) {
    if (!voiceIndicator) return;

    if (isActive) {
        voiceIndicator.classList.add('active');
        voiceIndicator.textContent = '🔴'; // 录音中
    } else {
        voiceIndicator.classList.remove('active');
        voiceIndicator.textContent = '🎤'; // 待机状态
    }
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 实时输入监听（input事件 - 支持语音输入）
    commandInput.addEventListener('input', handleInputChange);

    // 键盘事件监听
    commandInput.addEventListener('keydown', handleKeyDown);

    // 手动发送按钮
    sendButton.addEventListener('click', handleManualSend);

    // 清空按钮
    clearButton.addEventListener('click', handleClear);

    // 语音指示器点击事件
    if (voiceIndicator) {
        voiceIndicator.addEventListener('click', handleVoiceInputToggle);
    }

    // 输入框焦点事件
    commandInput.addEventListener('focus', () => {
        updateStatus('输入框已激活，支持键盘和语音输入...', 'success');
    });

    // 输入框失焦事件
    commandInput.addEventListener('blur', () => {
        // 如果正在进行语音输入，不要停止
        if (!isVoiceInputActive) {
            updateStatus('输入框失去焦点', '');
        }
    });

    // 监听Win+H等系统语音输入的变化
    // 通过监听输入框长度变化来检测语音输入
    setInterval(detectVoiceInput, 100);

    console.log('📡 事件监听器设置完成');
    console.log('🎤 语音输入监听已启动');
}

/**
 * 处理语音输入切换
 */
function handleVoiceInputToggle() {
    if (!speechRecognition) {
        updateStatus('浏览器不支持语音识别，请使用Win+H系统语音输入', 'error');
        return;
    }

    try {
        if (isVoiceInputActive) {
            // 停止语音识别
            speechRecognition.stop();
            console.log('🎤 手动停止语音识别');
        } else {
            // 开始语音识别
            speechRecognition.start();
            console.log('🎤 手动开始语音识别');
        }
    } catch (error) {
        console.error('❌ 语音识别操作失败:', error);
        updateStatus('语音识别操作失败', 'error');
    }
}

/**
 * 检测系统语音输入（如Win+H）
 * 通过监听输入框内容长度变化来检测
 */
function detectVoiceInput() {
    if (!commandInput) return;

    const currentLength = commandInput.value.length;

    // 检测到内容突然增加（可能是语音输入）
    if (currentLength > lastInputLength + 1 && !isVoiceInputActive) {
        console.log('🎤 检测到可能的系统语音输入');
        updateStatus('检测到语音输入，正在处理...', 'success');

        // 模拟语音输入状态
        updateVoiceIndicator(true);

        // 延迟恢复状态
        if (voiceInputTimeout) {
            clearTimeout(voiceInputTimeout);
        }

        voiceInputTimeout = setTimeout(() => {
            updateVoiceIndicator(false);
            updateStatus('语音输入处理完成', 'success');
        }, 1500);
    }

    lastInputLength = currentLength;
}

/**
 * 处理输入变化（实时传递）
 * 优化支持语音输入场景
 */
function handleInputChange(event) {
    const command = event.target.value.trim();

    console.log('📝 输入变化:', {
        command: command,
        length: command.length,
        isVoiceActive: isVoiceInputActive,
        lastCommand: lastCommand
    });

    // 防抖处理：避免过于频繁的发送
    // 语音输入时使用较短的延迟，键盘输入使用正常延迟
    const debounceDelay = isVoiceInputActive ? 150 : 300;

    if (sendTimeout) {
        clearTimeout(sendTimeout);
    }

    sendTimeout = setTimeout(async () => {
        // 避免重复发送相同指令
        if (command !== lastCommand) {
            lastCommand = command;

            if (command.length > 0) {
                try {
                    await sendCommandToContent(command, true); // true表示实时发送

                    // 根据输入方式显示不同的状态信息
                    const inputMethod = isVoiceInputActive ? '语音' : '键盘';
                    updateStatus(`${inputMethod}实时发送: "${command}"`, 'success');

                    console.log(`✅ ${inputMethod}指令发送成功:`, command);

                } catch (error) {
                    console.error('❌ 实时指令发送失败:', error);
                    updateStatus(`发送失败: ${error.message}`, 'error');
                }
            } else {
                updateStatus('输入为空，等待指令...', '');
            }
        }
    }, debounceDelay);
}

/**
 * 处理键盘事件
 */
function handleKeyDown(event) {
    // Enter键手动发送
    if (event.key === 'Enter') {
        event.preventDefault();
        handleManualSend();
    }

    // Escape键清空
    if (event.key === 'Escape') {
        event.preventDefault();
        handleClear();
    }
}

/**
 * 处理手动发送
 */
function handleManualSend() {
    const command = commandInput.value.trim();

    if (command.length === 0) {
        updateStatus('请输入指令内容', 'error');
        commandInput.focus();
        return;
    }

    sendCommandToContent(command, false); // false表示手动发送
    updateStatus(`手动发送: "${command}"`, 'success');

    console.log('📤 手动发送指令:', command);
}

/**
 * 处理清空操作
 */
function handleClear() {
    commandInput.value = '';
    lastCommand = '';
    updateStatus('已清空输入内容', '');
    commandInput.focus();

    // 发送清空指令到content script
    sendCommandToContent('', false, 'clear');

    console.log('🗑️ 已清空输入内容');
}

/**
 * 发送指令到content script
 * 支持语音输入和实时指令传递的优化版本
 */
async function sendCommandToContent(command, isRealtime = false, action = 'execute') {
    try {
        // 获取当前活动标签页
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        if (!tab) {
            throw new Error('无法获取当前标签页 - 请确保有活动的网页标签');
        }

        if (!tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
            throw new Error('无法在Chrome内部页面上执行指令');
        }

        // 构造增强的消息对象
        const message = {
            type: 'COMMAND_FROM_POPUP',
            command: command,
            isRealtime: isRealtime,
            action: action,
            timestamp: Date.now(),
            tabId: tab.id,
            tabUrl: tab.url,
            tabTitle: tab.title,
            inputMethod: isVoiceInputActive ? 'voice' : 'keyboard',
            version: '1.0.0'
        };

        console.log('📤 发送指令到content script:', {
            command: command,
            isRealtime: isRealtime,
            action: action,
            inputMethod: message.inputMethod,
            tabUrl: tab.url
        });

        // 发送消息到content script
        const response = await chrome.tabs.sendMessage(tab.id, message);

        if (response && response.success) {
            isConnected = true;
            console.log('✅ 指令发送成功:', {
                command: command,
                response: response.message,
                timestamp: message.timestamp
            });

            // 只在手动发送时显示成功状态
            if (!isRealtime) {
                const inputMethod = isVoiceInputActive ? '语音' : '手动';
                updateStatus(`${inputMethod}指令执行成功: "${command}"`, 'success');
            }

            return response;

        } else {
            throw new Error(response?.error || 'Content script响应异常');
        }

    } catch (error) {
        isConnected = false;

        // 详细的错误日志
        console.error('❌ 发送指令失败:', {
            error: error.message,
            command: command,
            isRealtime: isRealtime,
            action: action,
            timestamp: Date.now()
        });

        // 根据错误类型提供不同的处理方案
        let errorMessage = error.message;
        let shouldRetry = false;

        if (error.message.includes('Could not establish connection')) {
            errorMessage = 'Content script未加载，正在尝试重新注入...';
            shouldRetry = true;
        } else if (error.message.includes('Receiving end does not exist')) {
            errorMessage = '网页未准备就绪，正在重新连接...';
            shouldRetry = true;
        } else if (error.message.includes('chrome://')) {
            errorMessage = '无法在Chrome内部页面执行指令';
        }

        updateStatus(`发送失败: ${errorMessage}`, 'error');

        // 如果是连接错误，尝试重新注入content script
        if (shouldRetry) {
            await injectContentScript();
        }

        throw error; // 重新抛出错误供调用者处理
    }
}

/**
 * 注入content script（备用方案）
 */
async function injectContentScript() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        if (!tab) {
            throw new Error('无法获取当前标签页');
        }

        // 注入content script
        await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content.js']
        });

        updateStatus('Content script已重新注入', 'success');
        console.log('🔄 Content script重新注入成功');

    } catch (error) {
        console.error('❌ 注入content script失败:', error);
        updateStatus('无法连接到网页，请刷新页面后重试', 'error');
    }
}

/**
 * 检查连接状态
 */
async function checkConnection() {
    try {
        await sendCommandToContent('ping', false, 'ping');
        updateStatus('连接正常，可以开始使用', 'success');
    } catch (error) {
        updateStatus('正在建立连接...', '');
        // 尝试注入content script
        await injectContentScript();
    }
}

/**
 * 更新状态显示
 */
function updateStatus(message, type = '') {
    if (!statusText) return;

    statusText.textContent = message;
    statusText.className = `status-text ${type}`;

    console.log(`📊 状态更新: ${message} (${type})`);
}

/**
 * 页面加载完成后初始化
 */
document.addEventListener('DOMContentLoaded', initialize);

/**
 * 错误处理
 */
window.addEventListener('error', (event) => {
    console.error('❌ Popup脚本错误:', event.error);
    updateStatus('插件运行出错，请重新打开', 'error');
});

// 导出函数供调试使用
window.popupDebug = {
    sendCommand: sendCommandToContent,
    checkConnection: checkConnection,
    updateStatus: updateStatus,
    // 语音输入调试接口
    startVoiceInput: () => speechRecognition?.start(),
    stopVoiceInput: () => speechRecognition?.stop(),
    getVoiceStatus: () => ({
        isVoiceInputActive,
        speechRecognitionSupported: !!speechRecognition,
        lastInputLength
    }),
    // 状态查询接口
    getStatus: () => ({
        isConnected,
        lastCommand,
        isVoiceInputActive,
        speechRecognitionAvailable: !!speechRecognition
    })
};
