<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签页控制功能演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .demo-section {
            background: white;
            margin: 20px 0;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section h2 {
            color: #333;
            margin-top: 0;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .command-group {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        .command-group h3 {
            color: #495057;
            margin-top: 0;
        }
        .command {
            display: inline-block;
            background-color: #e9ecef;
            padding: 8px 12px;
            margin: 4px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border: 1px solid #dee2e6;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .command:hover {
            background-color: #667eea;
            color: white;
            transform: translateY(-1px);
        }
        .instructions {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            color: #2d3436;
        }
        .instructions h3 {
            margin-top: 0;
            color: #2d3436;
        }
        .status-panel {
            background: #2d3436;
            color: #ddd;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 20px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .quick-link {
            display: block;
            padding: 15px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .quick-link:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
        .feature-highlight {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .feature-highlight h3 {
            margin-top: 0;
            color: white;
        }
        .tab-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .tab-info h4 {
            color: #1976d2;
            margin-top: 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔄 Chrome扩展标签页控制功能演示</h1>
        <p>通过语音或文字指令控制浏览器标签页</p>
    </div>

    <div class="instructions">
        <h3>📋 使用说明</h3>
        <ol>
            <li><strong>打开多个标签页</strong>：点击下方的快速链接打开测试标签页</li>
            <li><strong>打开扩展</strong>：点击Chrome扩展图标打开popup界面</li>
            <li><strong>输入指令</strong>：在扩展中输入下方列出的测试指令</li>
            <li><strong>观察效果</strong>：查看标签页的变化和控制台日志</li>
        </ol>
    </div>

    <div class="demo-section">
        <h2>🚀 快速测试链接</h2>
        <p>点击下方链接在新标签页中打开，为标签页控制功能准备测试环境：</p>
        <div class="quick-links">
            <a href="https://www.baidu.com" target="_blank" class="quick-link">
                🔍 百度搜索
            </a>
            <a href="https://github.com" target="_blank" class="quick-link">
                💻 GitHub
            </a>
            <a href="https://www.google.com" target="_blank" class="quick-link">
                🌐 Google
            </a>
            <a href="https://stackoverflow.com" target="_blank" class="quick-link">
                ❓ Stack Overflow
            </a>
            <a href="https://developer.mozilla.org" target="_blank" class="quick-link">
                📚 MDN文档
            </a>
            <a href="https://www.wikipedia.org" target="_blank" class="quick-link">
                📖 维基百科
            </a>
        </div>
    </div>

    <div class="demo-section">
        <h2>1. 切换标签页指令</h2>
        
        <div class="command-group">
            <h3>按索引切换</h3>
            <span class="command">切换到第1个标签页</span>
            <span class="command">切换到第2个标签页</span>
            <span class="command">切换到第3个标签页</span>
            <span class="command">打开第4个标签页</span>
            <span class="command">跳转到第5个标签页</span>
        </div>

        <div class="command-group">
            <h3>按标题切换</h3>
            <span class="command">切换到百度标签页</span>
            <span class="command">切换到GitHub标签页</span>
            <span class="command">切换到Google标签页</span>
            <span class="command">打开维基百科标签页</span>
        </div>

        <div class="command-group">
            <h3>按URL切换</h3>
            <span class="command">切换到baidu.com标签页</span>
            <span class="command">切换到github.com标签页</span>
            <span class="command">切换到google.com标签页</span>
        </div>
    </div>

    <div class="demo-section">
        <h2>2. 关闭标签页指令</h2>
        
        <div class="warning">
            <strong>⚠️ 注意：</strong>关闭指令会真实关闭标签页，请谨慎测试！
        </div>

        <div class="command-group">
            <h3>关闭指定标签页</h3>
            <span class="command">关闭第2个标签页</span>
            <span class="command">关闭第3个标签页</span>
            <span class="command">删除第4个标签页</span>
        </div>

        <div class="command-group">
            <h3>关闭当前标签页</h3>
            <span class="command">关闭当前标签页</span>
            <span class="command">关闭标签页</span>
        </div>

        <div class="command-group">
            <h3>按标题关闭</h3>
            <span class="command">关闭百度标签页</span>
            <span class="command">关闭GitHub标签页</span>
        </div>
    </div>

    <div class="demo-section">
        <h2>3. 新建标签页指令</h2>
        
        <div class="command-group">
            <h3>创建空白标签页</h3>
            <span class="command">新建标签页</span>
            <span class="command">创建标签页</span>
            <span class="command">打开新标签页</span>
        </div>

        <div class="command-group">
            <h3>打开指定网站</h3>
            <span class="command">新建标签页到百度</span>
            <span class="command">新建标签页到GitHub</span>
            <span class="command">创建标签页到Google</span>
        </div>

        <div class="command-group">
            <h3>搜索内容</h3>
            <span class="command">新建标签页搜索JavaScript</span>
            <span class="command">新建标签页搜索Chrome扩展</span>
            <span class="command">创建标签页搜索前端开发</span>
        </div>
    </div>

    <div class="demo-section">
        <h2>4. 刷新标签页指令</h2>
        
        <div class="command-group">
            <h3>刷新当前标签页</h3>
            <span class="command">刷新当前标签页</span>
            <span class="command">重新加载当前标签页</span>
            <span class="command">重载当前标签页</span>
        </div>

        <div class="command-group">
            <h3>刷新指定标签页</h3>
            <span class="command">刷新第1个标签页</span>
            <span class="command">重新加载第2个标签页</span>
            <span class="command">重载第3个标签页</span>
        </div>
    </div>

    <div class="demo-section">
        <h2>5. 复制标签页指令</h2>
        
        <div class="command-group">
            <h3>复制当前标签页</h3>
            <span class="command">复制当前标签页</span>
            <span class="command">克隆当前标签页</span>
        </div>

        <div class="command-group">
            <h3>复制指定标签页</h3>
            <span class="command">复制第1个标签页</span>
            <span class="command">复制第2个标签页</span>
            <span class="command">克隆第3个标签页</span>
        </div>
    </div>

    <div class="feature-highlight">
        <h3>🎯 高级功能</h3>
        <p><strong>智能识别：</strong>扩展能够智能识别标签页指令，支持多种表达方式</p>
        <p><strong>错误处理：</strong>当标签页不存在或索引超出范围时，会显示友好的错误信息</p>
        <p><strong>实时反馈：</strong>每个操作都会在页面上显示执行结果</p>
        <p><strong>调试信息：</strong>在控制台中查看详细的执行日志</p>
    </div>

    <div class="tab-info">
        <h4>📊 标签页信息获取</h4>
        <p>在控制台中执行以下代码可以获取当前所有标签页的信息：</p>
        <div class="status-panel">
chrome.runtime.sendMessage({action: 'get_tabs_info'}, (response) => {
    console.log('标签页信息:', response);
});
        </div>
    </div>

    <div class="demo-section">
        <h2>🔍 调试和监控</h2>
        <p>打开开发者工具的Console标签页，可以看到详细的执行日志：</p>
        <div class="status-panel" id="console-output">
            等待指令执行...
        </div>
        <button onclick="clearConsole()" style="margin-top: 10px; padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">清空日志</button>
    </div>

    <script>
        // 监听扩展消息（如果有的话）
        let consoleOutput = document.getElementById('console-output');
        let logCount = 0;

        function addLog(message, type = 'info') {
            logCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            
            const currentContent = consoleOutput.textContent;
            if (currentContent === '等待指令执行...') {
                consoleOutput.textContent = logEntry;
            } else {
                consoleOutput.textContent = currentContent + '\n' + logEntry;
            }
            
            // 自动滚动到底部
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            
            // 限制日志条数
            if (logCount > 50) {
                const lines = consoleOutput.textContent.split('\n');
                consoleOutput.textContent = lines.slice(-40).join('\n');
                logCount = 40;
            }
        }

        function clearConsole() {
            consoleOutput.textContent = '等待指令执行...';
            logCount = 0;
        }

        // 为命令添加点击复制功能
        document.querySelectorAll('.command').forEach(cmd => {
            cmd.addEventListener('click', function() {
                const text = this.textContent;
                navigator.clipboard.writeText(text).then(() => {
                    addLog(`📋 已复制指令: "${text}"`);
                    this.style.backgroundColor = '#28a745';
                    this.style.color = 'white';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                        this.style.color = '';
                    }, 1000);
                }).catch(err => {
                    addLog(`❌ 复制失败: ${err.message}`, 'error');
                });
            });
        });

        // 页面加载完成提示
        window.addEventListener('load', function() {
            addLog('🔄 标签页控制演示页面已加载');
            addLog('💡 点击上方的指令可以复制到剪贴板');
            addLog('🚀 打开Chrome扩展开始测试标签页控制功能');
            
            // 检查是否有扩展
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                addLog('✅ 检测到Chrome扩展环境');
            } else {
                addLog('⚠️ 未检测到Chrome扩展环境');
            }
        });

        // 监听标签页变化（如果可能的话）
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                addLog('👁️ 标签页变为可见状态');
            } else {
                addLog('👁️ 标签页变为隐藏状态');
            }
        });
    </script>
</body>
</html>
