# 🧪 Content Script 测试指南

## 📋 功能概述

`content.js` 已完善，实现了以下核心功能：
- **消息监听**：使用 `chrome.runtime.onMessage.addListener` 监听来自 popup 的消息
- **指令解析**：支持语音和键盘输入的指令处理
- **详细调试**：完整的控制台日志输出
- **错误处理**：全面的 try-catch 错误捕获
- **响应机制**：标准的 `sendResponse` 返回格式

## 🚀 测试步骤

### 1. 基础连接测试

#### 1.1 加载插件并检查初始化
1. 重新加载Chrome插件
2. 打开任意网页（如 https://www.baidu.com）
3. 按 F12 打开开发者工具，切换到 Console 标签
4. 查看是否有以下初始化日志：

```
📦 Content script文件加载完成
🚀 准备初始化content script...
📄 DOM已加载完成，立即初始化
🚀 Content script初始化开始...
📍 当前页面URL: https://www.baidu.com/
📄 页面标题: 百度一下，你就知道
✅ Content script初始化完成
📡 消息监听器设置完成
🎯 等待来自popup的指令...
✅ Content script已加载，开始监听popup指令
```

#### 1.2 测试调试接口
在控制台中输入以下命令测试调试接口：

```javascript
// 检查状态
extensionDebug.getStatus()

// 获取调试信息
extensionDebug.getDebugInfo()

// 测试指令执行
extensionDebug.testCommand("hello")
```

### 2. 消息监听测试

#### 2.1 打开插件popup
1. 点击浏览器工具栏中的插件图标
2. 在输入框中输入测试指令
3. 观察控制台输出

#### 2.2 测试基本指令
依次测试以下指令，观察控制台日志：

**测试指令1：hello**
- 输入：`hello`
- 预期日志：
```
📨 收到popup指令: {command: "hello", isRealtime: true, inputMethod: "keyboard", ...}
⌨️ ⚡ [KEYBOARD指令] "hello"
🎯 开始执行指令:
   ⌨️ 指令内容: "hello"
   ⚡ 执行模式: 实时执行
   📱 输入方式: keyboard
🔍 开始解析指令: "hello"
📋 标准化后: "hello"
👋 执行欢迎指令
✅ 指令执行成功: "hello"
```

**测试指令2：点击登录按钮**
- 输入：`点击登录按钮`
- 预期日志：
```
🖱️ 收到测试指令: "点击登录按钮"
📝 指令内容: 点击登录按钮
💡 这是一个测试指令，暂未实现具体功能
```

**测试指令3：scroll down**
- 输入：`scroll down`
- 预期日志：
```
📜 执行向下滚动指令
✅ 指令执行成功: "scroll down"
📊 执行结果: 页面向下滚动200px
```

### 3. 语音输入测试

#### 3.1 使用Win+H语音输入
1. 点击插件输入框
2. 按 `Win + H` 启动Windows语音输入
3. 说出 "你好"
4. 观察控制台日志中的输入方式标识：

```
🎤 ⚡ [VOICE指令] "你好"
   📱 输入方式: voice
```

#### 3.2 使用浏览器语音识别
1. 点击插件中的麦克风图标 🎤
2. 说出指令
3. 观察语音识别结果和执行日志

### 4. 错误处理测试

#### 4.1 测试无效消息格式
在控制台中手动发送无效消息：

```javascript
chrome.runtime.sendMessage({type: 'INVALID_TYPE', command: 'test'}, (response) => {
    console.log('响应:', response);
});
```

预期响应：
```
{
    success: false,
    error: "无效的消息格式",
    expectedType: "COMMAND_FROM_POPUP",
    receivedType: "INVALID_TYPE"
}
```

#### 4.2 测试未知指令
- 输入：`未知指令测试`
- 预期日志：
```
❓ 收到指令: "未知指令测试"，暂未实现具体功能
📝 指令详情: {
    原始指令: "未知指令测试",
    标准化指令: "未知指令测试",
    输入方式: "keyboard",
    实时模式: true,
    时间戳: "14:30:25"
}
```

### 5. 响应机制测试

#### 5.1 成功响应格式验证
测试成功指令的响应格式：

```javascript
// 在popup.js的控制台中查看响应
window.popupDebug.sendCommand("hello", false).then(response => {
    console.log('成功响应:', response);
});
```

预期响应格式：
```
{
    success: true,
    message: "指令执行成功",
    result: "消息已显示: 👋 你好！插件正在正常工作！",
    executionTime: 15,
    inputMethod: "keyboard"
}
```

#### 5.2 失败响应格式验证
测试错误指令的响应格式（如果有的话）。

### 6. 高级功能测试

#### 6.1 指令历史记录
```javascript
// 执行几个指令后查看历史
extensionDebug.getHistory()

// 查看最近5条指令
extensionDebug.getLastCommands(5)
```

#### 6.2 调试模式测试
- 输入：`debug on`
- 观察页面左下角是否出现调试面板
- 输入：`debug off`
- 观察调试面板是否消失

#### 6.3 页面操作测试
- 输入：`highlight links` - 观察链接是否被高亮
- 输入：`page info` - 观察页面信息显示
- 输入：`clear highlights` - 观察高亮是否清除

### 7. 性能和兼容性测试

#### 7.1 重复指令处理
1. 快速连续输入相同指令
2. 观察是否有重复执行的日志：
```
⏭️ 跳过重复执行的实时指令
```

#### 7.2 不同网页测试
在以下网页上测试插件功能：
- 百度 (https://www.baidu.com)
- 谷歌 (https://www.google.com)
- GitHub (https://github.com)
- 本地HTML文件

#### 7.3 页面刷新测试
1. 刷新页面
2. 观察content script是否重新初始化
3. 测试插件功能是否正常

### 8. 调试技巧

#### 8.1 查看详细状态
```javascript
// 获取完整状态信息
extensionDebug.getDebugInfo()

// 检查是否准备就绪
extensionDebug.isReady()
```

#### 8.2 手动测试指令
```javascript
// 直接测试指令执行
extensionDebug.testCommand("scroll down")
extensionDebug.testCommand("点击登录按钮")
```

#### 8.3 清理测试环境
```javascript
// 清空指令历史
extensionDebug.clearHistory()

// 清除页面效果
extensionDebug.clearEffects()
```

## ✅ 验收标准

完成测试后，确认以下功能正常：

- ✅ 消息监听器正常工作
- ✅ 指令解析逻辑正确
- ✅ 控制台日志详细且格式正确
- ✅ 语音和键盘输入都能正确识别
- ✅ 错误处理机制完善
- ✅ 响应格式符合要求
- ✅ 调试接口功能完整
- ✅ 特殊测试指令正确处理
- ✅ 页面操作功能正常

## 🐛 常见问题排查

1. **消息监听器未响应**
   - 检查插件是否正确加载
   - 确认当前页面不是chrome://内部页面
   - 查看控制台是否有错误信息

2. **指令执行失败**
   - 检查指令格式是否正确
   - 查看详细的错误日志
   - 确认DOM元素是否存在

3. **调试接口不可用**
   - 刷新页面重新加载content script
   - 检查是否有JavaScript错误

---

**测试完成后，content.js应该能够完美处理来自popup的所有指令！** 🎉
