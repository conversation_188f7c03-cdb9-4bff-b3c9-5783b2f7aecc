# 🚀 实时指令插件

一个功能强大的Chrome浏览器插件，支持通过popup界面向网页发送实时指令，实现与网页的动态交互。

## ✨ 主要特性

- **🎤 智能语音快捷指令**：支持自然语言网站导航，如"打开我的邮箱"
- **📡 实时指令传递**：输入框内容变化时自动发送指令到网页
- **🖱️ 手动发送模式**：支持点击按钮或按Enter键手动发送指令
- **🌐 全网页支持**：可在任何网站上使用
- **⚡ 个人化快捷方式**：创建和管理自定义语音命令
- **📊 使用统计分析**：记录指令使用频率，优化常用指令
- **🔄 导入导出功能**：支持快捷指令配置的备份和恢复
- **🎯 智能模糊匹配**：即使表述不完全准确也能识别意图
- **🎨 美观的界面**：现代化的popup界面设计
- **🛡️ 错误处理**：完善的错误处理和状态反馈机制

## 📁 项目结构

```
chromePlugins/
├── manifest.json                # 插件配置文件 (Manifest V3)
├── popup.html                   # 弹出界面HTML
├── popup.css                    # 弹出界面样式
├── popup.js                     # 弹出界面逻辑（含智能语音快捷指令）
├── content.js                   # 内容脚本
├── background.js                # 后台脚本
├── README.md                    # 说明文档
├── VOICE_SHORTCUTS_GUIDE.md     # 智能语音快捷指令使用指南
├── voice_shortcuts_test.html    # 快捷指令功能测试页面
└── icons/                       # 插件图标文件夹
```

## 🛠️ 安装方法

1. **下载项目文件**
   - 确保所有文件都在同一个文件夹中

2. **打开Chrome扩展管理页面**
   - 在Chrome浏览器中输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

4. **加载插件**
   - 点击"加载已解压的扩展程序"
   - 选择包含插件文件的文件夹
   - 插件将自动加载并显示在扩展列表中

## 🧪 快速测试

安装完成后，可以使用以下方式快速测试功能：

1. **打开测试页面**：在浏览器中打开 `voice_shortcuts_test.html`
2. **测试语音快捷指令**：
   - 点击插件图标打开popup
   - 使用语音输入说"打开我的邮箱"
   - 或在输入框中输入"去淘宝"
3. **管理快捷指令**：
   - 点击"⚡ 快捷指令"按钮
   - 添加自定义指令，如"我的工作台" → "https://your-workspace.com"
4. **查看详细指南**：阅读 `VOICE_SHORTCUTS_GUIDE.md` 了解完整功能

## 🎯 使用方法

### 基本操作

1. **打开插件**
   - 点击浏览器工具栏中的插件图标
   - 或使用快捷键（如果已设置）

2. **语音快捷指令**
   - **语音输入**：点击麦克风图标或使用Win+H系统语音输入
   - **自然语言**：说出"打开我的邮箱"、"去淘宝"等日常用语
   - **智能匹配**：即使表述不完全准确也能识别意图

3. **传统指令模式**
   - **实时模式**：直接在输入框中输入，指令会自动发送
   - **手动模式**：输入后点击"手动发送"按钮或按Enter键

4. **快捷指令管理**
   - 点击"⚡ 快捷指令"按钮打开管理界面
   - 添加、编辑、删除个人化快捷指令
   - 导入/导出配置文件

5. **清空内容**
   - 点击"清空"按钮或按Escape键

### 智能语音快捷指令

| 语音指令 | 目标网站 | 说明 |
|----------|----------|------|
| "打开我的邮箱" / "进入邮箱" | Gmail | 邮箱相关 |
| "进入我的购物车" / "打开购物车" | 淘宝购物车 | 购物相关 |
| "去淘宝" / "打开京东" | 电商网站 | 购物平台 |
| "打开微博" / "去B站" | 社交媒体 | 社交娱乐 |
| "打开GitHub" / "进入云文档" | 工作平台 | 开发工作 |
| "百度搜索" / "谷歌搜索" | 搜索引擎 | 信息搜索 |

> 💡 **提示**：可以在"⚡ 快捷指令"管理界面中查看完整列表和添加自定义指令

### 传统页面操作指令

| 指令 | 功能 | 示例 |
|------|------|------|
| `hello` / `你好` | 显示欢迎消息 | 测试插件连接 |
| `scroll up` / `向上滚动` | 页面向上滚动200px | 快速向上浏览 |
| `scroll down` / `向下滚动` | 页面向下滚动200px | 快速向下浏览 |
| `scroll top` / `滚动到顶部` | 滚动到页面顶部 | 返回页面开始 |
| `scroll bottom` / `滚动到底部` | 滚动到页面底部 | 跳转到页面末尾 |
| `highlight links` / `高亮链接` | 高亮所有链接 | 快速识别可点击链接 |
| `highlight images` / `高亮图片` | 高亮所有图片 | 快速定位页面图片 |
| `clear highlights` / `清除高亮` | 清除所有高亮效果 | 恢复页面原貌 |
| `page info` / `页面信息` | 显示页面基本信息 | 查看页面统计 |
| `debug on` / `开启调试` | 开启调试模式 | 显示调试面板 |
| `debug off` / `关闭调试` | 关闭调试模式 | 隐藏调试面板 |

### 高级指令

- **点击元素**：`click [CSS选择器]`
  - 例如：`click button` - 点击第一个按钮
  - 例如：`click #submit` - 点击ID为submit的元素

- **隐藏元素**：`hide [CSS选择器]`
  - 例如：`hide .advertisement` - 隐藏所有广告类元素

- **显示元素**：`show [CSS选择器]`
  - 例如：`show .hidden-content` - 显示隐藏的内容

## 🔧 技术特性

### Manifest V3 支持
- 使用最新的Chrome扩展API
- 更好的安全性和性能
- 符合Chrome商店最新要求

### 双向通信机制
- Popup ↔ Content Script 实时通信
- 消息确认和错误处理
- 连接状态监控

### 防抖优化
- 300ms防抖延迟，避免过于频繁的指令发送
- 智能重复指令检测
- 性能优化的事件处理

### 错误恢复
- 自动重新注入Content Script
- 连接状态检测和恢复
- 详细的错误信息反馈

## 🎨 界面特色

- **现代化设计**：渐变背景、圆角边框、阴影效果
- **响应式布局**：适配不同屏幕尺寸
- **状态反馈**：实时显示操作状态和结果
- **动画效果**：流畅的界面过渡动画
- **无障碍支持**：键盘快捷键和焦点管理

## 🔍 调试功能

### 开发者工具
- 在popup.js中提供`window.popupDebug`调试接口
- 在content.js中提供`window.extensionDebug`调试接口

### 调试面板
- 使用`debug on`指令开启调试面板
- 显示页面信息、指令历史等调试数据

### 控制台日志
- 详细的控制台输出，便于开发调试
- 错误信息和状态变化的完整记录

## 🛡️ 权限说明

- **activeTab**：访问当前活动标签页
- **scripting**：注入和执行脚本
- **tabs**：管理浏览器标签页
- **storage**：存储快捷指令配置和使用统计
- **host_permissions**：在所有网站上运行

## 🚀 扩展开发

### 添加新指令
在`content.js`的`executeCommand`函数中添加新的case分支：

```javascript
case 'your-command':
    // 你的指令逻辑
    return '执行结果';
```

### 自定义样式
修改`popup.css`文件来自定义界面样式。

### 添加新功能
- 在`popup.js`中添加新的UI交互逻辑
- 在`content.js`中添加新的页面操作功能

## 📝 注意事项

1. **安全性**：插件只在用户主动操作时执行指令
2. **兼容性**：支持Chrome 88+版本
3. **性能**：使用防抖机制避免过度执行
4. **隐私**：不收集或传输任何用户数据

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License - 详见LICENSE文件

---

**享受使用实时指令插件！** 🎉
