<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时指令插件</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- 头部信息 -->
        <div class="header">
            <h1>🚀 实时指令插件</h1>
            <p>向当前网页发送实时指令</p>
        </div>

        <!-- 输入区域 -->
        <div class="input-section">
            <label for="commandInput" class="input-label">输入指令：</label>
            <div class="input-wrapper">
                <input
                    type="text"
                    id="commandInput"
                    class="command-input"
                    placeholder="输入指令或使用语音输入 (Win+H)..."
                    autocomplete="off"
                    spellcheck="false"
                    x-webkit-speech
                    webkitspeech
                    lang="zh-CN"
                    title="支持语音输入：点击麦克风图标或使用 Win+H 快捷键"
                >
                <div class="voice-indicator" id="voiceIndicator" title="语音输入状态">
                    🎤
                </div>
            </div>
            <div class="input-hint">
                💡 提示：支持语音输入 (Win+H) 和实时指令传递
            </div>
        </div>

        <!-- 按钮区域 -->
        <div class="button-section">
            <button type="button" id="sendButton" class="send-button">
                📤 发送指令
            </button>
            <button type="button" id="clearButton" class="clear-button">
                🗑️ 清空
            </button>
        </div>

        <!-- 状态显示区域 -->
        <div class="status-section">
            <p id="statusText" class="status-text">
                准备就绪 - 开始输入指令...
            </p>
        </div>

        <!-- 功能说明 -->
        <ul class="feature-list">
            <li>语音输入：支持Win+H语音输入和浏览器语音识别</li>
            <li>实时传递：输入时自动发送指令</li>
            <li>手动发送：点击按钮手动发送</li>
            <li>支持所有网页：在任何网站上都能使用</li>
            <li>即时响应：指令立即在网页上执行</li>
        </ul>
    </div>

    <script src="popup.js"></script>
</body>
</html>
