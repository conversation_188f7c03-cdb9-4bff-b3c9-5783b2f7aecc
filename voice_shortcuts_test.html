<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 智能语音快捷指令测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #4285f4;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #333;
            margin-top: 0;
            font-size: 20px;
        }

        .command-list {
            list-style: none;
            padding: 0;
        }

        .command-list li {
            background: white;
            margin: 8px 0;
            padding: 12px 16px;
            border-radius: 6px;
            border-left: 4px solid #4285f4;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .command-list li:hover {
            background: #f0f7ff;
            cursor: pointer;
        }

        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }

        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }

        .status-display {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        .success {
            color: #28a745;
        }

        .error {
            color: #dc3545;
        }

        .info {
            color: #17a2b8;
        }

        .btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background: #3367d6;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 智能语音快捷指令测试页面</h1>

        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>1. 确保已安装并启用了实时指令插件</p>
            <p>2. 点击插件图标打开popup界面</p>
            <p>3. 使用语音输入或键盘输入下方的测试指令</p>
            <p>4. 观察页面跳转和控制台日志</p>
            <p>5. 可以在插件的"⚡ 快捷指令"管理界面中查看和编辑指令</p>
        </div>

        <div class="test-section">
            <h2>🌐 网站导航测试指令</h2>
            <p>以下指令应该能够打开对应的网站：</p>
            <ul class="command-list">
                <li onclick="copyToClipboard(this)">打开我的邮箱</li>
                <li onclick="copyToClipboard(this)">进入邮箱</li>
                <li onclick="copyToClipboard(this)">查看邮件</li>
                <li onclick="copyToClipboard(this)">打开Gmail</li>
                <li onclick="copyToClipboard(this)">进入我的购物车</li>
                <li onclick="copyToClipboard(this)">打开购物车</li>
                <li onclick="copyToClipboard(this)">去淘宝</li>
                <li onclick="copyToClipboard(this)">打开京东</li>
                <li onclick="copyToClipboard(this)">打开微博</li>
                <li onclick="copyToClipboard(this)">进入微信网页版</li>
                <li onclick="copyToClipboard(this)">打开知乎</li>
                <li onclick="copyToClipboard(this)">去B站</li>
                <li onclick="copyToClipboard(this)">打开GitHub</li>
                <li onclick="copyToClipboard(this)">进入云文档</li>
                <li onclick="copyToClipboard(this)">百度搜索</li>
                <li onclick="copyToClipboard(this)">谷歌搜索</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🎯 模糊匹配测试指令</h2>
            <p>以下指令测试模糊匹配功能：</p>
            <ul class="command-list">
                <li onclick="copyToClipboard(this)">邮箱</li>
                <li onclick="copyToClipboard(this)">购物</li>
                <li onclick="copyToClipboard(this)">微博</li>
                <li onclick="copyToClipboard(this)">知乎</li>
                <li onclick="copyToClipboard(this)">B站</li>
                <li onclick="copyToClipboard(this)">GitHub</li>
                <li onclick="copyToClipboard(this)">搜索</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>⚙️ 管理功能测试</h2>
            <p>测试快捷指令管理功能：</p>
            <button type="button" class="btn" onclick="showInstructions('add')">添加自定义指令</button>
            <button type="button" class="btn" onclick="showInstructions('edit')">编辑指令</button>
            <button type="button" class="btn" onclick="showInstructions('export')">导出配置</button>
            <button type="button" class="btn" onclick="showInstructions('import')">导入配置</button>
            
            <div id="management-instructions" class="status-display" style="display: none;">
                <!-- 动态显示管理说明 -->
            </div>
        </div>

        <div class="test-section">
            <h2>📊 测试结果</h2>
            <p>测试结果和日志信息：</p>
            <button type="button" class="btn btn-success" onclick="clearLog()">清空日志</button>
            <div id="test-log" class="status-display">
                等待测试指令执行...
            </div>
        </div>
    </div>

    <script>
        // 复制指令到剪贴板
        function copyToClipboard(element) {
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                logMessage(`已复制指令: "${text}"`, 'info');
                element.style.background = '#d4edda';
                setTimeout(() => {
                    element.style.background = 'white';
                }, 1000);
            }).catch(err => {
                logMessage(`复制失败: ${err.message}`, 'error');
            });
        }

        // 显示管理说明
        function showInstructions(type) {
            const instructionsDiv = document.getElementById('management-instructions');
            let content = '';

            switch(type) {
                case 'add':
                    content = `
                        <h4>添加自定义指令步骤：</h4>
                        <ol>
                            <li>在插件popup中点击"⚡ 快捷指令"按钮</li>
                            <li>在"添加新快捷指令"区域输入指令名称</li>
                            <li>输入目标URL</li>
                            <li>点击"添加"按钮</li>
                            <li>测试新添加的指令</li>
                        </ol>
                    `;
                    break;
                case 'edit':
                    content = `
                        <h4>编辑指令步骤：</h4>
                        <ol>
                            <li>在快捷指令列表中找到要编辑的指令</li>
                            <li>点击"编辑"按钮</li>
                            <li>在弹出的对话框中修改URL</li>
                            <li>确认修改</li>
                        </ol>
                    `;
                    break;
                case 'export':
                    content = `
                        <h4>导出配置步骤：</h4>
                        <ol>
                            <li>在快捷指令管理界面</li>
                            <li>点击"📤 导出配置"按钮</li>
                            <li>保存下载的JSON文件</li>
                        </ol>
                    `;
                    break;
                case 'import':
                    content = `
                        <h4>导入配置步骤：</h4>
                        <ol>
                            <li>准备之前导出的JSON配置文件</li>
                            <li>点击"📥 导入配置"按钮</li>
                            <li>选择配置文件</li>
                            <li>确认导入</li>
                        </ol>
                    `;
                    break;
            }

            instructionsDiv.innerHTML = content;
            instructionsDiv.style.display = 'block';
        }

        // 记录日志
        function logMessage(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            
            const logEntry = document.createElement('div');
            logEntry.className = className;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            const logDiv = document.getElementById('test-log');
            logDiv.innerHTML = '日志已清空，等待新的测试指令...';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            logMessage('测试页面已加载，可以开始测试智能语音快捷指令功能', 'success');
            logMessage('提示：点击上方的指令可以复制到剪贴板', 'info');
        });

        // 监听插件消息（如果有的话）
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'SHORTCUT_EXECUTED') {
                logMessage(`快捷指令执行: ${event.data.command} → ${event.data.url}`, 'success');
            }
        });
    </script>
</body>
</html>
