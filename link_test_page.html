<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>链接点击功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        .link-group {
            margin: 15px 0;
        }
        .link-group h3 {
            color: #666;
            margin-bottom: 10px;
        }
        a {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .primary-link {
            background-color: #007bff;
            color: white;
        }
        .primary-link:hover {
            background-color: #0056b3;
        }
        .secondary-link {
            background-color: #6c757d;
            color: white;
        }
        .secondary-link:hover {
            background-color: #545b62;
        }
        .success-link {
            background-color: #28a745;
            color: white;
        }
        .success-link:hover {
            background-color: #1e7e34;
        }
        .warning-link {
            background-color: #ffc107;
            color: #212529;
        }
        .warning-link:hover {
            background-color: #e0a800;
        }
        .danger-link {
            background-color: #dc3545;
            color: white;
        }
        .danger-link:hover {
            background-color: #c82333;
        }
        .text-link {
            color: #007bff;
            text-decoration: underline;
            background: none;
            padding: 2px 4px;
        }
        .text-link:hover {
            color: #0056b3;
            background-color: #f8f9fa;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .test-commands {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .test-commands h4 {
            margin-top: 0;
            color: #495057;
        }
        .command {
            background-color: #e9ecef;
            padding: 4px 8px;
            border-radius: 3px;
            font-family: monospace;
            margin: 2px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <h1>🔗 Chrome扩展链接点击功能测试页面</h1>
    
    <div class="instructions">
        <h3>📋 测试说明</h3>
        <p>这个页面包含各种类型的链接，用于测试Chrome扩展的a标签点击功能。</p>
        <p><strong>使用方法：</strong></p>
        <ol>
            <li>打开Chrome扩展的popup界面</li>
            <li>输入下面列出的测试指令</li>
            <li>观察控制台日志和页面反应</li>
            <li>验证链接是否被正确识别和点击</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>1. 基础链接测试</h2>
        <div class="link-group">
            <h3>站内链接</h3>
            <a href="#section1" class="primary-link">首页链接</a>
            <a href="#about" class="secondary-link">关于我们</a>
            <a href="#contact" class="success-link">联系我们</a>
            <a href="#help" class="warning-link">帮助中心</a>
        </div>
        
        <div class="test-commands">
            <h4>测试指令：</h4>
            <span class="command">点击首页链接</span>
            <span class="command">点击关于我们</span>
            <span class="command">点击联系我们</span>
            <span class="command">点击帮助中心</span>
            <span class="command">打开首页链接</span>
            <span class="command">打开帮助中心</span>
        </div>
    </div>

    <div class="test-section">
        <h2>2. 外部链接测试</h2>
        <div class="link-group">
            <h3>外部网站</h3>
            <a href="https://www.baidu.com" target="_blank" class="primary-link" title="百度搜索">百度</a>
            <a href="https://www.google.com" target="_blank" class="secondary-link" title="谷歌搜索">谷歌</a>
            <a href="https://github.com" target="_blank" class="success-link" title="GitHub代码托管">GitHub</a>
            <a href="https://stackoverflow.com" target="_blank" class="warning-link" title="编程问答社区">Stack Overflow</a>
        </div>
        
        <div class="test-commands">
            <h4>测试指令：</h4>
            <span class="command">点击百度</span>
            <span class="command">点击谷歌</span>
            <span class="command">点击GitHub</span>
            <span class="command">打开百度</span>
            <span class="command">访问GitHub</span>
            <span class="command">进入Stack Overflow</span>
        </div>
    </div>

    <div class="test-section">
        <h2>3. 特殊属性链接测试</h2>
        <div class="link-group">
            <h3>带特殊属性的链接</h3>
            <a href="https://www.example.com" class="danger-link" aria-label="示例网站链接" title="这是一个示例网站">示例网站</a>
            <a href="mailto:<EMAIL>" class="text-link" title="发送邮件">发送邮件</a>
            <a href="tel:+86-138-0013-8000" class="text-link" title="拨打电话">拨打电话</a>
            <a href="javascript:alert('JavaScript链接被点击了！')" class="warning-link">JavaScript链接</a>
        </div>
        
        <div class="test-commands">
            <h4>测试指令：</h4>
            <span class="command">点击示例网站</span>
            <span class="command">点击发送邮件</span>
            <span class="command">点击拨打电话</span>
            <span class="command">点击JavaScript链接</span>
            <span class="command">打开示例网站</span>
        </div>
    </div>

    <div class="test-section">
        <h2>4. 文本链接测试</h2>
        <div class="link-group">
            <h3>普通文本样式链接</h3>
            <p>这里有一些文本中的链接：
                <a href="https://www.wikipedia.org" class="text-link">维基百科</a>、
                <a href="https://www.mozilla.org" class="text-link">Mozilla官网</a>、
                <a href="https://developer.mozilla.org" class="text-link">MDN文档</a>。
            </p>
            <p>还有一些特殊的链接：
                <a href="#" class="text-link" onclick="alert('空链接被点击')">空链接</a>、
                <a href="javascript:void(0)" class="text-link">无效链接</a>。
            </p>
        </div>
        
        <div class="test-commands">
            <h4>测试指令：</h4>
            <span class="command">点击维基百科</span>
            <span class="command">点击Mozilla官网</span>
            <span class="command">点击MDN文档</span>
            <span class="command">点击空链接</span>
            <span class="command">打开维基百科</span>
        </div>
    </div>

    <div class="test-section">
        <h2>5. 模糊匹配测试</h2>
        <div class="link-group">
            <h3>测试模糊匹配能力</h3>
            <a href="https://www.taobao.com" class="primary-link">淘宝购物网站</a>
            <a href="https://www.jd.com" class="secondary-link">京东商城</a>
            <a href="https://www.tmall.com" class="success-link">天猫超市</a>
            <a href="https://www.amazon.com" class="warning-link">亚马逊购物</a>
        </div>
        
        <div class="test-commands">
            <h4>测试指令（模糊匹配）：</h4>
            <span class="command">点击淘宝</span>
            <span class="command">点击购物</span>
            <span class="command">点击京东</span>
            <span class="command">点击商城</span>
            <span class="command">打开天猫</span>
            <span class="command">访问亚马逊</span>
        </div>
    </div>

    <div class="test-section">
        <h2>6. 混合元素测试</h2>
        <div class="link-group">
            <h3>链接与其他元素混合</h3>
            <button onclick="alert('这是按钮')">登录按钮</button>
            <a href="https://www.login.com" class="primary-link">登录链接</a>
            <input type="submit" value="提交按钮">
            <a href="https://www.submit.com" class="success-link">提交链接</a>
        </div>
        
        <div class="test-commands">
            <h4>测试指令（区分按钮和链接）：</h4>
            <span class="command">点击登录按钮</span>
            <span class="command">点击登录链接</span>
            <span class="command">点击提交按钮</span>
            <span class="command">点击提交链接</span>
            <span class="command">打开登录链接</span>
        </div>
    </div>

    <script>
        // 添加一些JavaScript来监听链接点击
        document.addEventListener('click', function(e) {
            if (e.target.tagName.toLowerCase() === 'a') {
                console.log('🔗 链接被点击:', {
                    文本: e.target.textContent,
                    链接: e.target.href,
                    目标: e.target.target || '当前窗口',
                    标题: e.target.title || '无标题'
                });
                
                // 对于测试链接，阻止实际跳转
                if (e.target.href.includes('example.com') || 
                    e.target.href.includes('baidu.com') ||
                    e.target.href.includes('google.com') ||
                    e.target.href.includes('github.com') ||
                    e.target.href.includes('stackoverflow.com') ||
                    e.target.href.includes('wikipedia.org') ||
                    e.target.href.includes('mozilla.org') ||
                    e.target.href.includes('taobao.com') ||
                    e.target.href.includes('jd.com') ||
                    e.target.href.includes('tmall.com') ||
                    e.target.href.includes('amazon.com') ||
                    e.target.href.includes('login.com') ||
                    e.target.href.includes('submit.com')) {
                    e.preventDefault();
                    alert(`测试成功！点击了链接: ${e.target.textContent}\n链接地址: ${e.target.href}`);
                }
            }
        });
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('🔗 链接测试页面加载完成');
            console.log('📋 可用的测试指令包括：');
            console.log('   - 点击[链接名称]');
            console.log('   - 打开[链接名称]');
            console.log('   - 访问[链接名称]');
            console.log('   - 进入[链接名称]');
        });
    </script>
</body>
</html>
